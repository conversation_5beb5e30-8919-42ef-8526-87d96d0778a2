---
description: 代码规范
globs: 
alwaysApply: false
---
这是一个关于前端项目规范的文件

# 目录结构
## 业务页面
### 将页面根据业务模块放到views目录下
## 组件
### 如果是全局的通用的布局组件，封装到compenents/layouts目录下
### 如果是业务模块组件，封装到业务模块页面路径下的components目录下
## 页面样式
### 将通用样式文件放到styles目录下
### 业务模块的样式放到业务模块页面路径下的styles目录下
## 资源文件
### 通用图片放到assets/img目录下
### 业务模块图片放到业务模块页面路径下的img目录下
## 路由
### 将路由文件按照业务模块命名放到router目录下
## API
### 将API按照业务模块命名放到api目录下
## MOCK
### 将MOCK数据按照业务模块命名放到mock目录下

# 生成要求
## 不要在没有经过我的允许的情况下，生成MD说明文件


