---
description: 项目描述
globs: 
alwaysApply: false
---
# 技术栈
## vue + vant

# 项目描述
## 本项目是一个MaaS平台后端管理系统的前端项目，终端为PC端

## 样式系统
### 动态主题色
项目支持动态主题色切换，通过URL参数传入或父系统消息传递：
- **默认主题色**: `#D7A256` (金棕色)
- **默认导航标签色**: `#FFF6E8` (浅米色)

### 主色调 (Primary Colors)
```css
/* 动态主题色 - 通过系统配置 */
--el-color-primary: #D7A256 (默认值)
--theme-color: #D7A256
--theme-color-hover: #D7A256

/* 文档规定的主色调 */
--primary-blue: #0075FF /* 蓝色 - 用于按钮边框、链接、强调文字等 */
```

#### 主题色应用场景
**#D7A256 (金棕色)** 主要用于：
- 表单标签文字颜色
- 按钮边框和背景色
- 重要信息强调色
- 进度指示器
- 链接和可点击元素
- 品牌标识元素

**#0075FF (蓝色)** 主要用于：
- 按钮边框和文字颜色
- 链接颜色
- 强调文字
- 某些按钮和标签的背景色
- 品牌标识的UI元素

### 辅助色彩 (Secondary Colors)
```css
/* 背景色系 */
--bg-primary: #f2f2f2     /* 浅灰色 - 页面背景色 */
--bg-secondary: #ffffff   /* 白色 - 卡片和对话框背景 */
--bg-tertiary: #fafdff    /* 输入框背景色 */
--bg-special: #eef3fe     /* 特殊背景色 */
--bg-tag: #FFF6E8         /* 标签背景色 */

/* 文字色系 */
--text-primary: #333333      /* 深灰色 - 主要文字颜色 */
--text-secondary: #666666    /* 中灰色 - 次要文字颜色 */
--text-tertiary: #999999     /* 浅灰色 - 辅助文字颜色 */
--text-quaternary: #84868c   /* 其他辅助文字 */
--text-placeholder: #a9a9a9  /* 占位符文字 */

/* 边框色系 */
--border-primary: #dcdfe6    /* 主要边框 */
--border-secondary: #e8e9eb  /* 次要边框 */
--border-light: #eee         /* 轻量边框 */
--border-special: #ccc       /* 特殊边框 */
```

### 渐变色系
```css
/* 页面背景渐变 */
--page-bg: linear-gradient(135deg, #f9f6f1 0%, #f3e7d9 100%)

/* 按钮渐变效果 */
--gradient-primary: linear-gradient(90deg, #dbab67 0%, #D7A256 100%)
--gradient-secondary: linear-gradient(90deg, #D7A256 0%, #dbab67 50%, #fbf6ee 100%)
--gradient-tertiary: linear-gradient(135deg, #dbab67 0%, #D7A256 100%)
```

### CSS变量命名规范
```css
/* Element Plus 主题变量 */
--el-color-primary
--el-color-primary-light-{1-9}
--el-color-primary-dark-2
--el-fill-color-light

/* 自定义主题变量 */
--theme-color
--theme-color-hover
--page-bg
--form-radius
--input-radius
--btn-radius
```

### 技术实现
项目通过以下方式实现动态主题：
1. **URL参数获取**: `themeColor` 和 `navTagColor`
2. **父系统消息监听**: 通过 `window.addEventListener('message')` 监听主题变化
3. **CSS变量设置**: 使用 `setThemeColor()` 函数动态设置CSS变量

### 相关文件
- `src/utils/themeColor.ts` - 主题色设置工具函数
- `src/stores/system.ts` - 系统配置状态管理
- `src/styles/colors.css` - 统一颜色配置文件
- `src/index.css` - 全局CSS工具类
- `docs/color-scheme.md` - 色彩规范文档
- `docs/color-preview.html` - 颜色预览页面
