import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    plugins: [
      vue(),
      AutoImport({
        resolvers: [ElementPlusResolver()],
        dts: false,
      }),
      Components({
        resolvers: [ElementPlusResolver()],
        dts: false,
      }),
    ],
    base: `${env.VITE_APP_ROOT}/`,
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    server: {
      host: '0.0.0.0',
      proxy: {
        '/dev-api': {
          changeOrigin: true,
          target: 'https://kbc-sta.kbao123.com',
          rewrite: (path) => path.replace(/^\/dev-api/, ''),
        },
        // '/dev-local': {
        //   changeOrigin: true,
        //   target: 'http://************:7000',
        //   rewrite: (path) => path.replace(/^\/dev-local/, ''),
        // },
      },
    },
  }
})
