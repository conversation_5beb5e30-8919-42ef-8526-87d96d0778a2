import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取调用日志列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getLogList(params) {
  return http.Axios.post(`${rootPath}/api/mcp/log/listAll`, params)
}

/**
 * 分页查询调用日志
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getLogPage(params) {
  return http.Axios.post(`${rootPath}/api/mcp/log/list`, params)
}

/**
 * 根据ID查询日志详情
 * @param {String} logId 日志ID
 * @returns {Promise}
 */
export function getLogDetail(logId) {
  return http.Axios.get(`${rootPath}/api/mcp/log/get?logId=${logId}`)
}

/**
 * 删除调用日志
 * @param {String} logId 日志ID
 * @returns {Promise}
 */
export function deleteLog(logId) {
  return http.Axios.post(`${rootPath}/api/mcp/log/delete?logId=${logId}`)
}

/**
 * 批量删除调用日志
 * @param {Array} logIds 日志ID数组
 * @returns {Promise}
 */
export function batchDeleteLogs(logIds) {
  return http.Axios.post(`${rootPath}/api/mcp/log/batchDelete`, { logIds })
}

/**
 * 清空调用日志
 * @param {Object} params 清空条件
 * @returns {Promise}
 */
export function clearLogs(params) {
  return http.Axios.post(`${rootPath}/api/mcp/log/clear`, params)
}

/**
 * 获取日志统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export function getLogStatistics(params) {
  return http.Axios.post(`${rootPath}/api/mcp/log/statistics`, params)
}

/**
 * 导出调用日志
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export function exportLogs(params) {
  return http.Axios({
    url: `${rootPath}/api/mcp/log/export`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}