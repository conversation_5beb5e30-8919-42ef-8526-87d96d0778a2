import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取应用服务器列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAppServerList(params) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/listAll`, params)
}

/**
 * 分页查询应用服务器
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAppServerPage(params) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/list`, params)
}

/**
 * 根据ID查询应用服务器详情
 * @param {String} appServerId 应用服务器ID
 * @returns {Promise}
 */
export function getAppServerDetail(appServerId) {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/get?appServerId=${appServerId}`)
}

/**
 * 根据编码查询应用服务器详情
 * @param {String} appServerCode 应用服务器编码
 * @returns {Promise}
 */
export function getAppServerDetailByCode(appServerCode) {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/getByCode?appServerCode=${appServerCode}`)
}

/**
 * 新增应用服务器
 * @param {Object} data 应用服务器数据
 * @returns {Promise}
 */
export function createAppServer(data) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/add`, data)
}

/**
 * 更新应用服务器
 * @param {Object} data 应用服务器数据
 * @returns {Promise}
 */
export function updateAppServer(data) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/update`, data)
}

/**
 * 删除应用服务器
 * @param {String} appServerId 应用服务器ID
 * @returns {Promise}
 */
export function deleteAppServer(appServerId) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/delete?appServerId=${appServerId}`)
}

/**
 * 批量删除应用服务器
 * @param {Array} appServerIds 应用服务器ID数组
 * @returns {Promise}
 */
export function batchDeleteAppServers(appServerIds) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/batchDelete`, { appServerIds })
}

/**
 * 更新应用服务器状态
 * @param {String} appServerId 应用服务器ID
 * @param {Number} status 状态值
 * @returns {Promise}
 */
export function updateAppServerStatus(appServerId, status) {
  return http.Axios.post(`${rootPath}/api/mcp/appserver/updateStatus`, { appServerId, status })
}

/**
 * 获取可用应用服务器列表
 * @returns {Promise}
 */
export function getAvailableAppServers() {
  return http.Axios.get(`${rootPath}/api/mcp/appserver/available`)
}