import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取工具列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getToolList(params) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/listAll`, params)
}

/**
 * 分页查询工具
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getToolPage(params) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/list`, params)
}

/**
 * 根据ID查询工具详情
 * @param {String} toolId 工具ID
 * @returns {Promise}
 */
export function getToolDetail(toolId) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/get?toolId=${toolId}`)
}

/**
 * 根据编码查询工具详情
 * @param {String} toolCode 工具编码
 * @returns {Promise}
 */
export function getToolDetailByCode(toolCode) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/getByCode?toolCode=${toolCode}`)
}

/**
 * 新增工具
 * @param {Object} data 工具数据
 * @returns {Promise}
 */
export function createTool(data) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/add`, data)
}

/**
 * 更新工具
 * @param {Object} data 工具数据
 * @returns {Promise}
 */
export function updateTool(data) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/update`, data)
}

/**
 * 删除工具
 * @param {String} toolId 工具ID
 * @returns {Promise}
 */
export function deleteTool(toolId) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/delete?toolId=${toolId}`)
}

/**
 * 更新工具状态
 * @param {String} toolId 工具ID
 * @param {Number} status 状态值
 * @returns {Promise}
 */
export function updateToolStatus(toolId, status) {
  return http.Axios.post(`${rootPath}/api/mcp/tool/updateStatus`, { toolId, status })
}

/**
 * 根据服务器ID获取工具列表
 * @param {String} serverId 服务器ID
 * @returns {Promise}
 */
export function getToolsByServerId(serverId) {
  return http.Axios.get(`${rootPath}/api/mcp/tool/getByServerId?serverId=${serverId}`)
}