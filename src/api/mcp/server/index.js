import http from '@/utils/httpService'
import { rootPath } from '@/utils/globalParam'

/**
 * 获取服务器列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getServerList(params) {
  return http.Axios.post(`${rootPath}/api/mcp/server/listAll`, params)
}

/**
 * 分页查询服务器
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getServerPage(params) {
  return http.Axios.post(`${rootPath}/api/mcp/server/list`, params)
}

/**
 * 根据ID查询服务器详情
 * @param {String} serverId 服务器ID
 * @returns {Promise}
 */
export function getServerDetail(serverId) {
  return http.Axios.get(`${rootPath}/api/mcp/server/get?serverId=${serverId}`)
}

/**
 * 根据编码查询服务器详情
 * @param {String} serverCode 服务器编码
 * @returns {Promise}
 */
export function getServerDetailByCode(serverCode) {
  return http.Axios.get(`${rootPath}/api/mcp/server/getByCode?serverCode=${serverCode}`)
}

/**
 * 新增服务器
 * @param {Object} data 服务器数据
 * @returns {Promise}
 */
export function createServer(data) {
  return http.Axios.post(`${rootPath}/api/mcp/server/add`, data)
}

/**
 * 更新服务器
 * @param {Object} data 服务器数据
 * @returns {Promise}
 */
export function updateServer(data) {
  return http.Axios.post(`${rootPath}/api/mcp/server/update`, data)
}

/**
 * 删除服务器
 * @param {String} serverId 服务器ID
 * @returns {Promise}
 */
export function deleteServer(serverId) {
  return http.Axios.post(`${rootPath}/api/mcp/server/delete?serverId=${serverId}`)
}

/**
 * 更新服务器状态
 * @param {String} serverId 服务器ID
 * @param {Number} status 状态值
 * @returns {Promise}
 */
export function updateServerStatus(serverId, status) {
  return http.Axios.post(`${rootPath}/api/mcp/server/updateStatus`, { serverId, status })
}

/**
 * 获取可用服务器列表
 * @returns {Promise}
 */
export function getAvailableServers() {
  return http.Axios.get(`${rootPath}/api/mcp/server/available`)
}