import type { TenantUser, Department, Role, WebUserInfo, TenantInitInfo } from './user'
import { 
  mockTenantUsers, 
  mockDepartments, 
  mockRoles, 
  mockCurrentWebUser, 
  mockTenantInitInfo 
} from './user'

/**
 * 用户管理模块MOCK服务
 * 对应接口：/web/bsc/tenant/getTenantUsers
 */

// ============= 工具函数 =============

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

const generateId = () => `id-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

const formatDate = () => new Date().toLocaleString('zh-CN', { 
  year: 'numeric', 
  month: '2-digit', 
  day: '2-digit', 
  hour: '2-digit', 
  minute: '2-digit', 
  second: '2-digit' 
}).replace(/\//g, '-')

const createSuccessResponse = (data: any, message = '操作成功') => ({
  status: 200,
  data,
  msg: message
})

const createErrorResponse = (message: string, status = 500) => ({
  status,
  data: null,
  msg: message
})

// ============= 租户用户管理 MOCK 服务 =============

export class TenantUserMockService {
  private users: TenantUser[] = [...mockTenantUsers]
  private departments: Department[] = [...mockDepartments]
  private roles: Role[] = [...mockRoles]

  /**
   * 获取租户用户列表
   * 对应接口：GET /web/bsc/tenant/getTenantUsers
   */
  async getTenantUsers(params: any) {
    await delay(300)
    const { 
      pageNum = 1, 
      pageSize = 10, 
      tenantId,
      userName,
      nickName,
      email,
      phone,
      roleId,
      departmentId,
      status
    } = params
    
    let filteredUsers = [...this.users]
    
    // 按租户ID过滤（必填）
    if (tenantId) {
      filteredUsers = filteredUsers.filter(u => u.tenantId === tenantId)
    }
    
    // 按用户名过滤
    if (userName) {
      filteredUsers = filteredUsers.filter(u => 
        u.userName.toLowerCase().includes(userName.toLowerCase())
      )
    }
    
    // 按昵称过滤
    if (nickName) {
      filteredUsers = filteredUsers.filter(u => 
        u.nickName.includes(nickName)
      )
    }
    
    // 按邮箱过滤
    if (email) {
      filteredUsers = filteredUsers.filter(u => 
        u.email.toLowerCase().includes(email.toLowerCase())
      )
    }
    
    // 按手机号过滤
    if (phone) {
      filteredUsers = filteredUsers.filter(u => 
        u.phone.includes(phone)
      )
    }
    
    // 按角色过滤
    if (roleId) {
      filteredUsers = filteredUsers.filter(u => u.roleId === roleId)
    }
    
    // 按部门过滤
    if (departmentId) {
      filteredUsers = filteredUsers.filter(u => u.departmentId === departmentId)
    }
    
    // 按状态过滤
    if (status) {
      filteredUsers = filteredUsers.filter(u => u.status === status)
    }
    
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredUsers.slice(start, end)
    
    return createSuccessResponse({
      list,
      total: filteredUsers.length,
      pageNum,
      pageSize,
      summary: {
        totalUsers: filteredUsers.length,
        activeUsers: filteredUsers.filter(u => u.status === 'active').length,
        inactiveUsers: filteredUsers.filter(u => u.status === 'inactive').length,
        pendingUsers: filteredUsers.filter(u => u.status === 'pending').length,
        adminUsers: filteredUsers.filter(u => u.isAdmin).length
      }
    }, '查询成功')
  }

  /**
   * 获取用户详情
   */
  async getUserDetail(params: { userId: string }) {
    await delay(200)
    const user = this.users.find(u => u.userId === params.userId)
    if (!user) {
      throw createErrorResponse('用户不存在', 404)
    }
    
    return createSuccessResponse(user, '查询成功')
  }

  /**
   * 添加租户用户
   */
  async addTenantUser(data: Partial<TenantUser>) {
    await delay(500)
    
    // 检查用户名是否已存在
    const existingUser = this.users.find(u => 
      u.userName === data.userName && u.tenantId === data.tenantId
    )
    if (existingUser) {
      throw createErrorResponse('用户名已存在')
    }
    
    // 检查邮箱是否已存在
    const existingEmail = this.users.find(u => u.email === data.email)
    if (existingEmail) {
      throw createErrorResponse('邮箱已被使用')
    }
    
    const newUser: TenantUser = {
      id: generateId(),
      userId: generateId(),
      userName: data.userName!,
      nickName: data.nickName!,
      email: data.email!,
      phone: data.phone!,
      avatar: data.avatar,
      tenantId: data.tenantId!,
      tenantName: data.tenantName!,
      roleId: data.roleId!,
      roleName: data.roleName!,
      departmentId: data.departmentId,
      departmentName: data.departmentName,
      status: data.status || 'pending',
      isAdmin: data.isAdmin || false,
      createTime: formatDate(),
      updateTime: formatDate()
    }
    
    this.users.push(newUser)
    return createSuccessResponse(newUser, '添加成功')
  }

  /**
   * 更新租户用户
   */
  async updateTenantUser(data: Partial<TenantUser>) {
    await delay(400)
    const index = this.users.findIndex(u => u.userId === data.userId)
    if (index === -1) {
      throw createErrorResponse('用户不存在', 404)
    }
    
    // 如果更新邮箱，检查是否已被其他用户使用
    if (data.email && data.email !== this.users[index].email) {
      const existingEmail = this.users.find(u => 
        u.email === data.email && u.userId !== data.userId
      )
      if (existingEmail) {
        throw createErrorResponse('邮箱已被使用')
      }
    }
    
    this.users[index] = {
      ...this.users[index],
      ...data,
      updateTime: formatDate()
    }
    
    return createSuccessResponse(this.users[index], '更新成功')
  }

  /**
   * 删除租户用户
   */
  async deleteTenantUser(params: { userId: string }) {
    await delay(300)
    const index = this.users.findIndex(u => u.userId === params.userId)
    if (index === -1) {
      throw createErrorResponse('用户不存在', 404)
    }
    
    // 不能删除管理员用户
    if (this.users[index].isAdmin) {
      throw createErrorResponse('不能删除管理员用户')
    }
    
    this.users.splice(index, 1)
    return createSuccessResponse(null, '删除成功')
  }

  /**
   * 批量更新用户状态
   */
  async batchUpdateUserStatus(params: { userIds: string[], status: string }) {
    await delay(600)
    const { userIds, status } = params
    let updatedCount = 0
    
    userIds.forEach(userId => {
      const index = this.users.findIndex(u => u.userId === userId)
      if (index !== -1) {
        this.users[index].status = status as any
        this.users[index].updateTime = formatDate()
        updatedCount++
      }
    })
    
    return createSuccessResponse({
      updatedCount,
      totalCount: userIds.length
    }, `成功更新${updatedCount}个用户状态`)
  }

  /**
   * 重置用户密码
   */
  async resetUserPassword(params: { userId: string }) {
    await delay(800)
    const user = this.users.find(u => u.userId === params.userId)
    if (!user) {
      throw createErrorResponse('用户不存在', 404)
    }
    
    // 模拟生成临时密码
    const tempPassword = Math.random().toString(36).slice(-8)
    
    return createSuccessResponse({
      userId: params.userId,
      tempPassword,
      message: '密码已重置，请及时通知用户修改密码'
    }, '密码重置成功')
  }

  /**
   * 获取部门列表
   */
  async getDepartments() {
    await delay(200)
    return createSuccessResponse(this.departments, '查询成功')
  }

  /**
   * 获取角色列表
   */
  async getRoles() {
    await delay(200)
    return createSuccessResponse(this.roles, '查询成功')
  }

  /**
   * 获取用户统计信息
   */
  async getUserStatistics(params: { tenantId: string }) {
    await delay(300)
    const tenantUsers = this.users.filter(u => u.tenantId === params.tenantId)
    
    const statistics = {
      totalUsers: tenantUsers.length,
      activeUsers: tenantUsers.filter(u => u.status === 'active').length,
      inactiveUsers: tenantUsers.filter(u => u.status === 'inactive').length,
      pendingUsers: tenantUsers.filter(u => u.status === 'pending').length,
      adminUsers: tenantUsers.filter(u => u.isAdmin).length,
      departmentStats: this.departments.map(dept => ({
        departmentId: dept.id,
        departmentName: dept.name,
        userCount: tenantUsers.filter(u => u.departmentId === dept.id).length
      })),
      roleStats: this.roles.map(role => ({
        roleId: role.id,
        roleName: role.name,
        userCount: tenantUsers.filter(u => u.roleId === role.id).length
      })),
      recentLogins: tenantUsers
        .filter(u => u.lastLoginTime)
        .sort((a, b) => new Date(b.lastLoginTime!).getTime() - new Date(a.lastLoginTime!).getTime())
        .slice(0, 10)
        .map(u => ({
          userId: u.userId,
          userName: u.userName,
          nickName: u.nickName,
          lastLoginTime: u.lastLoginTime
        }))
    }
    
    return createSuccessResponse(statistics, '查询成功')
  }
}

// ============= Web用户信息 MOCK 服务 =============

export class WebUserMockService {
  private currentUser: WebUserInfo = { ...mockCurrentWebUser }

  /**
   * 获取当前登录用户信息
   * 对应接口：GET /web/bsc/getWebUserInfo
   */
  async getWebUserInfo() {
    await delay(200)
    
    // 模拟token验证
    const tokenValid = Math.random() > 0.05 // 95%成功率
    if (!tokenValid) {
      throw createErrorResponse('Token已过期，请重新登录', 401)
    }
    
    // 更新最后登录时间
    this.currentUser.lastLoginTime = formatDate()
    
    return createSuccessResponse({
      user: this.currentUser,
      serverTime: formatDate(),
      tokenExpireTime: this.currentUser.tokenExpireTime
    }, '获取用户信息成功')
  }

  /**
   * 更新用户信息
   */
  async updateWebUserInfo(data: Partial<WebUserInfo>) {
    await delay(400)
    
    // 验证邮箱格式
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      throw createErrorResponse('邮箱格式不正确')
    }
    
    // 验证手机格式
    if (data.phone && !/^1[3-9]\d{9}$/.test(data.phone)) {
      throw createErrorResponse('手机号格式不正确')
    }
    
    this.currentUser = {
      ...this.currentUser,
      ...data,
      updateTime: formatDate()
    }
    
    return createSuccessResponse(this.currentUser, '更新成功')
  }

  /**
   * 修改密码
   */
  async changePassword(data: { oldPassword: string, newPassword: string }) {
    await delay(600)
    
    // 模拟密码验证
    if (data.oldPassword !== 'admin123') {
      throw createErrorResponse('原密码错误')
    }
    
    // 验证新密码强度
    if (data.newPassword.length < 8) {
      throw createErrorResponse('新密码长度不能少于8位')
    }
    
    return createSuccessResponse(null, '密码修改成功，请重新登录')
  }
}

// ============= 租户初始化 MOCK 服务 =============

export class TenantInitMockService {
  private tenantInfo: TenantInitInfo = { ...mockTenantInitInfo }

  /**
   * 租户初始化
   * 对应接口：GET /web/bsc/tenant/init
   */
  async tenantInit(params?: { tenantId?: string }) {
    await delay(400)
    
    // 如果提供了tenantId，可以模拟不同租户的信息
    if (params?.tenantId && params.tenantId !== 'T0001') {
      // 返回不同租户的信息
      const altTenantInfo: TenantInitInfo = {
        ...this.tenantInfo,
        tenantId: params.tenantId,
        tenantName: '测试租户',
        tenantCode: 'TEST_TENANT',
        tenantLogo: 'https://via.placeholder.com/120x120/E74C3C/FFFFFF?text=TEST',
        currentUsers: 3,
        currentBots: 5,
        usedStorage: 512,
        features: [
          'smart_chat',
          'knowledge_base',
          'multi_channel'
        ]
      }
      return createSuccessResponse(altTenantInfo, '租户初始化成功')
    }
    
    // 模拟系统状态检查
    const systemStatus = {
      database: 'healthy',
      cache: 'healthy',
      storage: 'healthy',
      mcp: 'healthy'
    }
    
    // 获取系统配置
    const systemConfig = {
      version: '2.1.0',
      buildTime: '2024-01-20 10:30:00',
      environment: 'production',
      supportContact: '<EMAIL>',
      documentUrl: 'https://docs.kbao123.com'
    }
    
    // 检查租户状态
    if (this.tenantInfo.tenantStatus === 'expired') {
      throw createErrorResponse('租户已过期，请联系管理员续费', 403)
    }
    
    if (this.tenantInfo.tenantStatus === 'inactive') {
      throw createErrorResponse('租户已禁用，请联系管理员', 403)
    }
    
    return createSuccessResponse({
      tenant: this.tenantInfo,
      systemStatus,
      systemConfig,
      initTime: formatDate()
    }, '租户初始化成功')
  }

  /**
   * 更新租户配置
   */
  async updateTenantConfig(data: Partial<TenantInitInfo>) {
    await delay(500)
    
    this.tenantInfo = {
      ...this.tenantInfo,
      ...data,
      updateTime: formatDate()
    }
    
    return createSuccessResponse(this.tenantInfo, '配置更新成功')
  }

  /**
   * 获取租户使用统计
   */
  async getTenantUsage() {
    await delay(300)
    
    const usage = {
      users: {
        total: this.tenantInfo.currentUsers,
        limit: this.tenantInfo.maxUsers,
        percentage: Math.round((this.tenantInfo.currentUsers / this.tenantInfo.maxUsers) * 100)
      },
      bots: {
        total: this.tenantInfo.currentBots,
        limit: this.tenantInfo.maxBots,
        percentage: Math.round((this.tenantInfo.currentBots / this.tenantInfo.maxBots) * 100)
      },
      storage: {
        used: this.tenantInfo.usedStorage,
        limit: this.tenantInfo.maxStorage,
        percentage: Math.round((this.tenantInfo.usedStorage / this.tenantInfo.maxStorage) * 100)
      },
      monthlyStats: {
        apiCalls: 125430,
        messagesSent: 89670,
        activeUsers: 45,
        averageResponseTime: 1.2 // 秒
      }
    }
    
    return createSuccessResponse(usage, '查询成功')
  }

  /**
   * 检查功能权限
   */
  async checkFeaturePermission(feature: string) {
    await delay(100)
    
    const hasPermission = this.tenantInfo.features.includes(feature)
    
    return createSuccessResponse({
      feature,
      hasPermission,
      tenantId: this.tenantInfo.tenantId
    }, hasPermission ? '功能可用' : '功能不可用')
  }
}

// ============= 统一导出 =============

export const userMockServices = {
  tenantUser: new TenantUserMockService(),
  webUser: new WebUserMockService(),
  tenantInit: new TenantInitMockService()
}
