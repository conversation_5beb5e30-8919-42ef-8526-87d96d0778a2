/**
 * 用户管理模块MOCK数据
 * 对应接口：/web/bsc/tenant/getTenantUsers
 */

// ============= 用户相关类型定义 =============

export interface WebUserInfo {
  id: string
  userId: string
  userName: string
  nickName: string
  realName: string
  email: string
  phone: string
  avatar?: string
  tenantId: string
  tenantName: string
  roleId: string
  roleName: string
  departmentId?: string
  departmentName?: string
  permissions: string[]
  isAdmin: boolean
  status: 'active' | 'inactive'
  lastLoginTime?: string
  lastLoginIp?: string
  tokenExpireTime: string
  createTime: string
  updateTime: string
}

export interface TenantInitInfo {
  tenantId: string
  tenantName: string
  tenantCode: string
  tenantLogo?: string
  tenantStatus: 'active' | 'inactive' | 'expired'
  expireTime?: string
  maxUsers: number
  currentUsers: number
  maxBots: number
  currentBots: number
  maxStorage: number // MB
  usedStorage: number // MB
  features: string[]
  config: {
    allowRegister: boolean
    allowInvite: boolean
    defaultRole: string
    passwordPolicy: {
      minLength: number
      requireSpecialChar: boolean
      requireNumber: boolean
      requireUppercase: boolean
    }
    sessionTimeout: number // 分钟
    maxLoginAttempts: number
  }
  contactInfo: {
    adminName: string
    adminEmail: string
    adminPhone: string
    supportEmail: string
  }
  createTime: string
  updateTime: string
}

export interface TenantUser {
  id: string
  userId: string
  userName: string
  nickName: string
  email: string
  phone: string
  avatar?: string
  tenantId: string
  tenantName: string
  roleId: string
  roleName: string
  departmentId?: string
  departmentName?: string
  status: 'active' | 'inactive' | 'pending'
  isAdmin: boolean
  lastLoginTime?: string
  createTime: string
  updateTime: string
}

export interface Department {
  id: string
  name: string
  parentId?: string
  level: number
  sort: number
  description?: string
}

export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: string[]
}

// ============= MOCK数据定义 =============

export const mockTenantUsers: TenantUser[] = [
  {
    id: 'tu-001',
    userId: 'user-001',
    userName: 'admin',
    nickName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://avatars.dicebear.com/api/avataaars/admin.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-001',
    roleName: '超级管理员',
    departmentId: 'dept-001',
    departmentName: '技术部',
    status: 'active',
    isAdmin: true,
    lastLoginTime: '2024-01-23 09:30:00',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-23 09:30:00'
  },
  {
    id: 'tu-002',
    userId: 'user-002',
    userName: 'zhangsan',
    nickName: '张三',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://avatars.dicebear.com/api/avataaars/zhangsan.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-002',
    roleName: '智能体管理员',
    departmentId: 'dept-002',
    departmentName: '产品部',
    status: 'active',
    isAdmin: false,
    lastLoginTime: '2024-01-23 08:45:00',
    createTime: '2024-01-05 14:20:00',
    updateTime: '2024-01-20 16:30:00'
  },
  {
    id: 'tu-003',
    userId: 'user-003',
    userName: 'lisi',
    nickName: '李四',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: 'https://avatars.dicebear.com/api/avataaars/lisi.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-003',
    roleName: '客服专员',
    departmentId: 'dept-003',
    departmentName: '客服部',
    status: 'active',
    isAdmin: false,
    lastLoginTime: '2024-01-22 17:20:00',
    createTime: '2024-01-08 11:15:00',
    updateTime: '2024-01-22 17:20:00'
  },
  {
    id: 'tu-004',
    userId: 'user-004',
    userName: 'wangwu',
    nickName: '王五',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: 'https://avatars.dicebear.com/api/avataaars/wangwu.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-004',
    roleName: '数据分析师',
    departmentId: 'dept-004',
    departmentName: '数据部',
    status: 'active',
    isAdmin: false,
    lastLoginTime: '2024-01-23 10:15:00',
    createTime: '2024-01-10 09:30:00',
    updateTime: '2024-01-23 10:15:00'
  },
  {
    id: 'tu-005',
    userId: 'user-005',
    userName: 'zhaoliu',
    nickName: '赵六',
    email: '<EMAIL>',
    phone: '13800138005',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-003',
    roleName: '客服专员',
    departmentId: 'dept-003',
    departmentName: '客服部',
    status: 'inactive',
    isAdmin: false,
    lastLoginTime: '2024-01-15 16:45:00',
    createTime: '2024-01-12 13:40:00',
    updateTime: '2024-01-20 09:10:00'
  },
  {
    id: 'tu-006',
    userId: 'user-006',
    userName: 'qianqi',
    nickName: '钱七',
    email: '<EMAIL>',
    phone: '13800138006',
    avatar: 'https://avatars.dicebear.com/api/avataaars/qianqi.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-005',
    roleName: '测试工程师',
    departmentId: 'dept-001',
    departmentName: '技术部',
    status: 'pending',
    isAdmin: false,
    createTime: '2024-01-22 15:20:00',
    updateTime: '2024-01-22 15:20:00'
  },
  {
    id: 'tu-007',
    userId: 'user-007',
    userName: 'sunba',
    nickName: '孙八',
    email: '<EMAIL>',
    phone: '13800138007',
    avatar: 'https://avatars.dicebear.com/api/avataaars/sunba.svg',
    tenantId: 'T0001',
    tenantName: '演示租户',
    roleId: 'role-006',
    roleName: '运营专员',
    departmentId: 'dept-005',
    departmentName: '运营部',
    status: 'active',
    isAdmin: false,
    lastLoginTime: '2024-01-23 11:20:00',
    createTime: '2024-01-15 10:45:00',
    updateTime: '2024-01-23 11:20:00'
  },
  {
    id: 'tu-008',
    userId: 'user-008',
    userName: 'zhoujiu',
    nickName: '周九',
    email: '<EMAIL>',
    phone: '13800138008',
    tenantId: 'T0002',
    tenantName: '测试租户',
    roleId: 'role-002',
    roleName: '智能体管理员',
    departmentId: 'dept-006',
    departmentName: '业务部',
    status: 'active',
    isAdmin: false,
    lastLoginTime: '2024-01-22 14:30:00',
    createTime: '2024-01-18 16:20:00',
    updateTime: '2024-01-22 14:30:00'
  }
]

export const mockDepartments: Department[] = [
  {
    id: 'dept-001',
    name: '技术部',
    level: 1,
    sort: 1,
    description: '负责系统开发和技术支持'
  },
  {
    id: 'dept-002',
    name: '产品部',
    level: 1,
    sort: 2,
    description: '负责产品规划和设计'
  },
  {
    id: 'dept-003',
    name: '客服部',
    level: 1,
    sort: 3,
    description: '负责客户服务和支持'
  },
  {
    id: 'dept-004',
    name: '数据部',
    level: 1,
    sort: 4,
    description: '负责数据分析和挖掘'
  },
  {
    id: 'dept-005',
    name: '运营部',
    level: 1,
    sort: 5,
    description: '负责产品运营和推广'
  },
  {
    id: 'dept-006',
    name: '业务部',
    level: 1,
    sort: 6,
    description: '负责业务拓展和客户管理'
  }
]

export const mockRoles: Role[] = [
  {
    id: 'role-001',
    name: '超级管理员',
    code: 'super_admin',
    description: '系统最高权限管理员',
    permissions: ['*']
  },
  {
    id: 'role-002',
    name: '智能体管理员',
    code: 'bot_admin',
    description: '智能体配置和管理权限',
    permissions: ['bot:read', 'bot:write', 'bot:config', 'mcp:read', 'mcp:write']
  },
  {
    id: 'role-003',
    name: '客服专员',
    code: 'customer_service',
    description: '客服相关功能权限',
    permissions: ['bot:read', 'chat:read', 'customer:read']
  },
  {
    id: 'role-004',
    name: '数据分析师',
    code: 'data_analyst',
    description: '数据查看和分析权限',
    permissions: ['data:read', 'report:read', 'analytics:read']
  },
  {
    id: 'role-005',
    name: '测试工程师',
    code: 'test_engineer',
    description: '测试相关功能权限',
    permissions: ['bot:read', 'test:read', 'test:write']
  },
  {
    id: 'role-006',
    name: '运营专员',
    code: 'operation_specialist',
    description: '运营相关功能权限',
    permissions: ['content:read', 'content:write', 'analytics:read']
  }
]

// ============= 当前登录用户信息 =============

export const mockCurrentWebUser: WebUserInfo = {
  id: 'wu-001',
  userId: 'user-001',
  userName: 'admin',
  nickName: '系统管理员',
  realName: '张管理',
  email: '<EMAIL>',
  phone: '13800138001',
  avatar: 'https://avatars.dicebear.com/api/avataaars/admin.svg',
  tenantId: 'T0001',
  tenantName: '演示租户',
  roleId: 'role-001',
  roleName: '超级管理员',
  departmentId: 'dept-001',
  departmentName: '技术部',
  permissions: ['*'],
  isAdmin: true,
  status: 'active',
  lastLoginTime: '2024-01-23 09:30:00',
  lastLoginIp: '*************',
  tokenExpireTime: '2024-01-24 09:30:00',
  createTime: '2024-01-01 10:00:00',
  updateTime: '2024-01-23 09:30:00'
}

// ============= 租户初始化信息 =============

export const mockTenantInitInfo: TenantInitInfo = {
  tenantId: 'T0001',
  tenantName: '演示租户',
  tenantCode: 'DEMO_TENANT',
  tenantLogo: 'https://via.placeholder.com/120x120/4A90E2/FFFFFF?text=DEMO',
  tenantStatus: 'active',
  expireTime: '2024-12-31 23:59:59',
  maxUsers: 100,
  currentUsers: 8,
  maxBots: 50,
  currentBots: 12,
  maxStorage: 10240, // 10GB
  usedStorage: 2048,  // 2GB
  features: [
    'smart_chat',      // 智能对话
    'mcp_tools',       // MCP工具
    'knowledge_base',  // 知识库
    'data_analytics',  // 数据分析
    'custom_flows',    // 自定义流程
    'multi_channel',   // 多渠道接入
    'api_integration', // API集成
    'advanced_config'  // 高级配置
  ],
  config: {
    allowRegister: false,
    allowInvite: true,
    defaultRole: 'role-003',
    passwordPolicy: {
      minLength: 8,
      requireSpecialChar: true,
      requireNumber: true,
      requireUppercase: true
    },
    sessionTimeout: 480, // 8小时
    maxLoginAttempts: 5
  },
  contactInfo: {
    adminName: '张管理',
    adminEmail: '<EMAIL>',
    adminPhone: '13800138001',
    supportEmail: '<EMAIL>'
  },
  createTime: '2024-01-01 10:00:00',
  updateTime: '2024-01-20 16:30:00'
}
