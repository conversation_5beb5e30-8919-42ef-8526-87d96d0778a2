import type { McpServer, McpTool, RobotMcpConfig } from '@/api/mcp'
import { 
  mockMcpServers, 
  mockMcpTools, 
  mockRobotMcpConfigs, 
  mockMcpExecutionLogs 
} from './mcp'

/**
 * MCP模块MOCK服务
 * 提供模拟API调用的服务函数
 */

// ============= 工具函数 =============

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

const generateId = () => `id-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

const formatDate = () => new Date().toLocaleString('zh-CN', { 
  year: 'numeric', 
  month: '2-digit', 
  day: '2-digit', 
  hour: '2-digit', 
  minute: '2-digit', 
  second: '2-digit' 
}).replace(/\//g, '-')

const createSuccessResponse = (data: any, message = '操作成功') => ({
  status: 200,
  data,
  msg: message
})

const createErrorResponse = (message: string, status = 500) => ({
  status,
  data: null,
  msg: message
})

// ============= MCP服务器管理 MOCK 服务 =============

export class McpServerMockService {
  private servers: McpServer[] = [...mockMcpServers]

  async getServerList(params: any) {
    await delay(300)
    const { pageNum = 1, pageSize = 10, param = {} } = params
    let filteredServers = [...this.servers]
    
    // 按名称过滤
    if (param.name) {
      filteredServers = filteredServers.filter(s => 
        s.name.includes(param.name)
      )
    }
    
    // 按状态过滤
    if (param.status) {
      filteredServers = filteredServers.filter(s => s.status === param.status)
    }
    
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredServers.slice(start, end)
    
    return createSuccessResponse({
      list,
      total: filteredServers.length,
      pageNum,
      pageSize
    }, '查询成功')
  }

  async addServer(data: McpServer) {
    await delay(500)
    const newServer = {
      ...data,
      id: generateId(),
      createTime: formatDate(),
      updateTime: formatDate()
    }
    this.servers.push(newServer)
    return createSuccessResponse(newServer, '添加成功')
  }

  async updateServer(data: Partial<McpServer>) {
    await delay(400)
    const index = this.servers.findIndex(s => s.id === data.id)
    if (index === -1) {
      throw createErrorResponse('服务器不存在', 404)
    }
    
    this.servers[index] = {
      ...this.servers[index],
      ...data,
      updateTime: formatDate()
    }
    
    return createSuccessResponse(this.servers[index], '更新成功')
  }

  async deleteServer(params: { id: string }) {
    await delay(300)
    const index = this.servers.findIndex(s => s.id === params.id)
    if (index === -1) {
      throw createErrorResponse('服务器不存在', 404)
    }
    
    this.servers.splice(index, 1)
    return createSuccessResponse(null, '删除成功')
  }

  async getServerDetail(params: { id: string }) {
    await delay(200)
    const server = this.servers.find(s => s.id === params.id)
    if (!server) {
      throw createErrorResponse('服务器不存在', 404)
    }
    
    return createSuccessResponse(server, '查询成功')
  }

  async testConnection(params: { id: string }) {
    await delay(1000) // 模拟网络延迟
    const server = this.servers.find(s => s.id === params.id)
    if (!server) {
      throw createErrorResponse('服务器不存在', 404)
    }
    
    // 模拟连接测试结果 - 80%成功率
    const success = Math.random() > 0.2
    return createSuccessResponse({
      success,
      message: success ? '连接成功' : '连接失败：网络超时',
      responseTime: Math.floor(Math.random() * 1000) + 100
    }, '测试完成')
  }
}

// ============= MCP工具管理 MOCK 服务 =============

export class McpToolMockService {
  private tools: McpTool[] = [...mockMcpTools]

  async getToolList(params: any) {
    await delay(300)
    const { pageNum = 1, pageSize = 10, param = {} } = params
    let filteredTools = [...this.tools]
    
    // 按名称过滤
    if (param.name) {
      filteredTools = filteredTools.filter(t => 
        t.name.includes(param.name) || t.displayName.includes(param.name)
      )
    }
    
    // 按服务器过滤
    if (param.serverId) {
      filteredTools = filteredTools.filter(t => t.serverId === param.serverId)
    }
    
    // 按分类过滤
    if (param.category) {
      filteredTools = filteredTools.filter(t => t.category === param.category)
    }
    
    // 按状态过滤
    if (param.status) {
      filteredTools = filteredTools.filter(t => t.status === param.status)
    }
    
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredTools.slice(start, end)
    
    return createSuccessResponse({
      list,
      total: filteredTools.length,
      pageNum,
      pageSize
    }, '查询成功')
  }

  async getToolsByServer(params: { serverId: string }) {
    await delay(300)
    const tools = this.tools.filter(t => t.serverId === params.serverId)
    return createSuccessResponse(tools, '查询成功')
  }

  async updateTool(data: Partial<McpTool>) {
    await delay(400)
    const index = this.tools.findIndex(t => t.id === data.id)
    if (index === -1) {
      throw createErrorResponse('工具不存在', 404)
    }
    
    this.tools[index] = {
      ...this.tools[index],
      ...data,
      updateTime: formatDate()
    }
    
    return createSuccessResponse(this.tools[index], '更新成功')
  }

  async testTool(params: { toolId: string, params: any }) {
    await delay(800)
    const tool = this.tools.find(t => t.id === params.toolId)
    if (!tool) {
      throw createErrorResponse('工具不存在', 404)
    }
    
    // 模拟工具执行结果 - 85%成功率
    const success = Math.random() > 0.15
    if (!success) {
      throw createErrorResponse('工具执行失败：参数验证错误')
    }
    
    // 根据工具类型返回不同的模拟结果
    let result
    switch (tool.name) {
      case 'getUserInfo':
        result = {
          id: params.params.userId || 'user123',
          name: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          profile: params.params.includeProfile ? { age: 28, city: '北京' } : undefined
        }
        break
      case 'searchOrders':
        result = {
          total: 3,
          orders: [
            { id: 'order-1', userId: params.params.userId || 'user123', amount: 299.00, status: 'pending', createTime: '2024-01-22 10:00:00' },
            { id: 'order-2', userId: params.params.userId || 'user123', amount: 159.00, status: 'completed', createTime: '2024-01-21 15:30:00' }
          ]
        }
        break
      case 'processPayment':
        result = {
          paymentId: `pay_${generateId()}`,
          status: 'pending',
          transactionId: `txn_${generateId()}`,
          paymentUrl: 'https://payment.example.com/pay/12345'
        }
        break
      case 'uploadFile':
        result = {
          fileId: `file_${generateId()}`,
          uploadUrl: 'https://upload.example.com/upload/12345',
          accessUrl: 'https://files.example.com/files/12345'
        }
        break
      default:
        result = { 
          success: true, 
          message: '执行成功', 
          data: params.params,
          timestamp: new Date().toISOString()
        }
    }
    
    return createSuccessResponse(result, '测试成功')
  }

  getAvailableTools() {
    return this.tools.filter(t => t.status === 'active')
  }
}

// ============= 智能体MCP配置 MOCK 服务 =============

export class RobotMcpConfigMockService {
  private configs: Record<string, RobotMcpConfig> = { ...mockRobotMcpConfigs }

  async getRobotConfig(params: { robotId: string }) {
    await delay(200)
    const config = this.configs[params.robotId]
    return createSuccessResponse(config || null, '查询成功')
  }

  async saveRobotConfig(data: RobotMcpConfig) {
    await delay(500)
    const configWithMeta = {
      ...data,
      id: data.id || generateId(),
      createTime: data.createTime || formatDate(),
      updateTime: formatDate()
    }
    
    this.configs[data.robotId] = configWithMeta
    return createSuccessResponse(configWithMeta, '保存成功')
  }

  async getRobotAvailableTools(params: { robotId: string }) {
    await delay(300)
    const toolService = new McpToolMockService()
    const availableTools = toolService.getAvailableTools()
    return createSuccessResponse(availableTools, '查询成功')
  }
}

// ============= MCP执行日志 MOCK 服务 =============

export class McpExecutionLogMockService {
  private logs = [...mockMcpExecutionLogs]

  async getExecutionLog(params: any) {
    await delay(300)
    const { pageNum = 1, pageSize = 10, param = {} } = params
    let filteredLogs = [...this.logs]
    
    // 按工具名称过滤
    if (param.toolName) {
      filteredLogs = filteredLogs.filter(l => 
        l.toolName.includes(param.toolName)
      )
    }
    
    // 按智能体过滤
    if (param.robotName) {
      filteredLogs = filteredLogs.filter(l => 
        l.robotName.includes(param.robotName)
      )
    }
    
    // 按状态过滤
    if (param.status) {
      filteredLogs = filteredLogs.filter(l => l.status === param.status)
    }
    
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredLogs.slice(start, end)
    
    return createSuccessResponse({
      list,
      total: filteredLogs.length,
      pageNum,
      pageSize
    }, '查询成功')
  }
}

// ============= 统一MOCK API导出 =============

export const mcpMockServices = {
  server: new McpServerMockService(),
  tool: new McpToolMockService(),
  robotConfig: new RobotMcpConfigMockService(),
  executionLog: new McpExecutionLogMockService()
}
