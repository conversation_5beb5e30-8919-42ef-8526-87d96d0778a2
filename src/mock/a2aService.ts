// A2A协议多智能体协同相关的MOCK服务

import { 
  mockA2AProtocolConfigs, 
  mockAgentCollaborationTasks, 
  mockAgentCommunications, 
  mockCollaborationMetrics,
  A2AProtocolConfig,
  AgentCollaborationTask,
  AgentCommunication,
  CollaborationMetrics
} from './a2a'

// 模拟网络延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// A2A协议配置管理服务
class A2AProtocolMockService {
  private configs: A2AProtocolConfig[] = [...mockA2AProtocolConfigs]

  async getProtocolList(params: any) {
    await delay(300 + Math.random() * 200)
    
    const { pageNum = 1, pageSize = 10, name, status } = params
    let filteredConfigs = [...this.configs]

    // 名称搜索
    if (name) {
      filteredConfigs = filteredConfigs.filter(config => 
        config.name.toLowerCase().includes(name.toLowerCase())
      )
    }

    // 状态筛选
    if (status) {
      filteredConfigs = filteredConfigs.filter(config => config.status === status)
    }

    // 分页
    const total = filteredConfigs.length
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredConfigs.slice(start, end)

    return {
      status: 200,
      data: {
        list,
        total,
        pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize)
      },
      message: '获取A2A协议配置列表成功'
    }
  }

  async getProtocolDetail(id: string) {
    await delay(200 + Math.random() * 100)
    
    const config = this.configs.find(c => c.id === id)
    if (!config) {
      return {
        status: 404,
        data: null,
        message: 'A2A协议配置不存在'
      }
    }

    return {
      status: 200,
      data: config,
      message: '获取A2A协议配置详情成功'
    }
  }

  async createProtocol(data: Partial<A2AProtocolConfig>) {
    await delay(500 + Math.random() * 300)
    
    const newConfig: A2AProtocolConfig = {
      id: `a2a-${Date.now()}`,
      name: data.name || '',
      version: data.version || '1.0.0',
      description: data.description || '',
      endpoint: data.endpoint || '',
      authType: data.authType || 'none',
      authConfig: data.authConfig || {},
      timeout: data.timeout || 30000,
      retryCount: data.retryCount || 3,
      status: 'inactive',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.configs.unshift(newConfig)

    return {
      status: 200,
      data: newConfig,
      message: '创建A2A协议配置成功'
    }
  }

  async updateProtocol(id: string, data: Partial<A2AProtocolConfig>) {
    await delay(400 + Math.random() * 200)
    
    const index = this.configs.findIndex(c => c.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: 'A2A协议配置不存在'
      }
    }

    this.configs[index] = {
      ...this.configs[index],
      ...data,
      updatedAt: new Date().toISOString()
    }

    return {
      status: 200,
      data: this.configs[index],
      message: '更新A2A协议配置成功'
    }
  }

  async deleteProtocol(id: string) {
    await delay(300 + Math.random() * 150)
    
    const index = this.configs.findIndex(c => c.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: 'A2A协议配置不存在'
      }
    }

    this.configs.splice(index, 1)

    return {
      status: 200,
      data: null,
      message: '删除A2A协议配置成功'
    }
  }

  async testConnection(id: string) {
    await delay(2000 + Math.random() * 1000)
    
    const config = this.configs.find(c => c.id === id)
    if (!config) {
      return {
        status: 404,
        data: null,
        message: 'A2A协议配置不存在'
      }
    }

    // 模拟连接测试结果
    const success = Math.random() > 0.2 // 80% 成功率
    
    if (success) {
      return {
        status: 200,
        data: {
          connected: true,
          responseTime: Math.floor(100 + Math.random() * 500),
          version: config.version,
          capabilities: ['message_passing', 'task_coordination', 'status_monitoring']
        },
        message: 'A2A协议连接测试成功'
      }
    } else {
      return {
        status: 500,
        data: {
          connected: false,
          error: 'Connection timeout',
          lastError: new Date().toISOString()
        },
        message: 'A2A协议连接测试失败'
      }
    }
  }
}

// 智能体协同任务管理服务
class AgentCollaborationMockService {
  private tasks: AgentCollaborationTask[] = [...mockAgentCollaborationTasks]

  async getTaskList(params: any) {
    await delay(400 + Math.random() * 300)
    
    const { pageNum = 1, pageSize = 10, name, status, type, priority } = params
    let filteredTasks = [...this.tasks]

    // 名称搜索
    if (name) {
      filteredTasks = filteredTasks.filter(task => 
        task.name.toLowerCase().includes(name.toLowerCase())
      )
    }

    // 状态筛选
    if (status) {
      filteredTasks = filteredTasks.filter(task => task.status === status)
    }

    // 类型筛选
    if (type) {
      filteredTasks = filteredTasks.filter(task => task.type === type)
    }

    // 优先级筛选
    if (priority) {
      filteredTasks = filteredTasks.filter(task => task.priority === priority)
    }

    // 按更新时间倒序排列
    filteredTasks.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())

    // 分页
    const total = filteredTasks.length
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredTasks.slice(start, end)

    return {
      status: 200,
      data: {
        list,
        total,
        pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize)
      },
      message: '获取智能体协同任务列表成功'
    }
  }

  async getTaskDetail(id: string) {
    await delay(300 + Math.random() * 200)
    
    const task = this.tasks.find(t => t.id === id)
    if (!task) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    return {
      status: 200,
      data: task,
      message: '获取协同任务详情成功'
    }
  }

  async createTask(data: Partial<AgentCollaborationTask>) {
    await delay(600 + Math.random() * 400)
    
    const newTask: AgentCollaborationTask = {
      id: `task-${Date.now()}`,
      name: data.name || '',
      description: data.description || '',
      type: data.type || 'sequential',
      status: 'draft',
      priority: data.priority || 'medium',
      participants: data.participants || [],
      workflow: data.workflow || [],
      createdBy: 'current-user',
      assignedTo: data.assignedTo || [],
      progress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.tasks.unshift(newTask)

    return {
      status: 200,
      data: newTask,
      message: '创建协同任务成功'
    }
  }

  async updateTask(id: string, data: Partial<AgentCollaborationTask>) {
    await delay(500 + Math.random() * 300)
    
    const index = this.tasks.findIndex(t => t.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    this.tasks[index] = {
      ...this.tasks[index],
      ...data,
      updatedAt: new Date().toISOString()
    }

    return {
      status: 200,
      data: this.tasks[index],
      message: '更新协同任务成功'
    }
  }

  async deleteTask(id: string) {
    await delay(400 + Math.random() * 200)
    
    const index = this.tasks.findIndex(t => t.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    this.tasks.splice(index, 1)

    return {
      status: 200,
      data: null,
      message: '删除协同任务成功'
    }
  }

  async executeTask(id: string) {
    await delay(1000 + Math.random() * 500)
    
    const index = this.tasks.findIndex(t => t.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    // 模拟任务执行
    this.tasks[index].status = 'active'
    this.tasks[index].startTime = new Date().toISOString()
    this.tasks[index].updatedAt = new Date().toISOString()

    return {
      status: 200,
      data: this.tasks[index],
      message: '任务执行成功'
    }
  }

  async pauseTask(id: string) {
    await delay(300 + Math.random() * 200)
    
    const index = this.tasks.findIndex(t => t.id === id)
    if (index === -1) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    this.tasks[index].status = 'paused'
    this.tasks[index].updatedAt = new Date().toISOString()

    return {
      status: 200,
      data: this.tasks[index],
      message: '任务暂停成功'
    }
  }

  async getTaskMetrics(id: string) {
    await delay(500 + Math.random() * 300)
    
    const task = this.tasks.find(t => t.id === id)
    if (!task) {
      return {
        status: 404,
        data: null,
        message: '协同任务不存在'
      }
    }

    const metrics = mockCollaborationMetrics.filter(m => m.taskId === id)

    return {
      status: 200,
      data: {
        task: task,
        metrics: metrics,
        summary: {
          avgResponseTime: metrics.reduce((sum, m) => sum + m.metrics.responseTime, 0) / metrics.length,
          avgSuccessRate: metrics.reduce((sum, m) => sum + m.metrics.successRate, 0) / metrics.length,
          avgAvailability: metrics.reduce((sum, m) => sum + m.metrics.availability, 0) / metrics.length,
          totalThroughput: metrics.reduce((sum, m) => sum + m.metrics.throughput, 0)
        }
      },
      message: '获取任务性能指标成功'
    }
  }
}

// 智能体通信记录服务
class AgentCommunicationMockService {
  private communications: AgentCommunication[] = [...mockAgentCommunications]

  async getCommunicationList(params: any) {
    await delay(300 + Math.random() * 200)
    
    const { pageNum = 1, pageSize = 10, taskId, fromAgentId, toAgentId, messageType, protocol } = params
    let filteredComms = [...this.communications]

    // 任务ID筛选
    if (taskId) {
      filteredComms = filteredComms.filter(comm => comm.taskId === taskId)
    }

    // 发送者筛选
    if (fromAgentId) {
      filteredComms = filteredComms.filter(comm => comm.fromAgentId === fromAgentId)
    }

    // 接收者筛选
    if (toAgentId) {
      filteredComms = filteredComms.filter(comm => comm.toAgentId === toAgentId)
    }

    // 消息类型筛选
    if (messageType) {
      filteredComms = filteredComms.filter(comm => comm.messageType === messageType)
    }

    // 协议类型筛选
    if (protocol) {
      filteredComms = filteredComms.filter(comm => comm.protocol === protocol)
    }

    // 按时间倒序排列
    filteredComms.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // 分页
    const total = filteredComms.length
    const start = (pageNum - 1) * pageSize
    const end = start + pageSize
    const list = filteredComms.slice(start, end)

    return {
      status: 200,
      data: {
        list,
        total,
        pageNum,
        pageSize,
        pages: Math.ceil(total / pageSize)
      },
      message: '获取智能体通信记录成功'
    }
  }

  async getCommunicationDetail(id: string) {
    await delay(200 + Math.random() * 100)
    
    const comm = this.communications.find(c => c.id === id)
    if (!comm) {
      return {
        status: 404,
        data: null,
        message: '通信记录不存在'
      }
    }

    return {
      status: 200,
      data: comm,
      message: '获取通信记录详情成功'
    }
  }

  async getCommunicationStats(params: any) {
    await delay(400 + Math.random() * 300)
    
    const { taskId, timeRange = '24h' } = params
    let filteredComms = [...this.communications]

    if (taskId) {
      filteredComms = filteredComms.filter(comm => comm.taskId === taskId)
    }

    // 统计数据
    const stats = {
      total: filteredComms.length,
      byMessageType: {
        request: filteredComms.filter(c => c.messageType === 'request').length,
        response: filteredComms.filter(c => c.messageType === 'response').length,
        notification: filteredComms.filter(c => c.messageType === 'notification').length,
        error: filteredComms.filter(c => c.messageType === 'error').length
      },
      byProtocol: {
        A2A: filteredComms.filter(c => c.protocol === 'A2A').length,
        HTTP: filteredComms.filter(c => c.protocol === 'HTTP').length,
        WebSocket: filteredComms.filter(c => c.protocol === 'WebSocket').length,
        gRPC: filteredComms.filter(c => c.protocol === 'gRPC').length
      },
      byStatus: {
        sent: filteredComms.filter(c => c.status === 'sent').length,
        delivered: filteredComms.filter(c => c.status === 'delivered').length,
        processed: filteredComms.filter(c => c.status === 'processed').length,
        failed: filteredComms.filter(c => c.status === 'failed').length
      },
      avgResponseTime: filteredComms.filter(c => c.responseTime).reduce((sum, c) => sum + (c.responseTime || 0), 0) / filteredComms.filter(c => c.responseTime).length || 0
    }

    return {
      status: 200,
      data: stats,
      message: '获取通信统计数据成功'
    }
  }
}

// 导出服务实例
export const a2aMockServices = {
  protocol: new A2AProtocolMockService(),
  collaboration: new AgentCollaborationMockService(),
  communication: new AgentCommunicationMockService()
}
