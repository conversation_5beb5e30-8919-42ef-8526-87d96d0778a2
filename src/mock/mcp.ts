import type { McpServer, McpTool, RobotMcpConfig } from '@/api/mcp'

/**
 * MCP模块MOCK数据
 * 按照代码规范，将MOCK数据统一放在mock目录下
 */

// ============= MOCK数据存储 =============

export const mockMcpServers: McpServer[] = [
  {
    id: 'server-1',
    name: '业务系统A',
    description: '提供用户管理、订单查询等核心业务接口',
    url: 'https://business-a.example.com/mcp',
    authType: 'api-key',
    authConfig: {
      apiKey: 'ak-12345678',
      headerName: 'X-API-Key'
    },
    status: 'active',
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-20 14:20:00'
  },
  {
    id: 'server-2',
    name: '第三方支付服务',
    description: '支付处理、退款、订单状态查询',
    url: 'https://payment.example.com/mcp',
    authType: 'bearer',
    authConfig: {
      token: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    },
    status: 'active',
    createTime: '2024-01-16 09:15:00',
    updateTime: '2024-01-18 16:45:00'
  },
  {
    id: 'server-3',
    name: '数据分析平台',
    description: '数据查询、报表生成、统计分析',
    url: 'https://analytics.example.com/mcp',
    authType: 'basic',
    authConfig: {
      username: 'api_user',
      password: 'api_pass123'
    },
    status: 'inactive',
    createTime: '2024-01-10 11:00:00',
    updateTime: '2024-01-22 13:30:00'
  },
  {
    id: 'server-4',
    name: '文件存储服务',
    description: '文件上传、下载、管理功能',
    url: 'https://files.example.com/mcp',
    authType: 'none',
    authConfig: {},
    status: 'active',
    createTime: '2024-01-12 15:20:00',
    updateTime: '2024-01-19 10:10:00'
  },
  {
    id: 'server-5',
    name: '智能推荐引擎',
    description: '基于用户行为的个性化推荐服务',
    url: 'https://recommend.ai-platform.com/mcp',
    authType: 'api-key',
    authConfig: {
      apiKey: 'rec-98765432',
      headerName: 'Authorization'
    },
    status: 'active',
    createTime: '2024-01-18 14:00:00',
    updateTime: '2024-01-23 09:45:00'
  },
  {
    id: 'server-6',
    name: '消息通知中心',
    description: '邮件、短信、站内信等多渠道消息推送',
    url: 'https://notification.example.com/mcp',
    authType: 'bearer',
    authConfig: {
      token: 'Bearer nt-token-abc123xyz'
    },
    status: 'active',
    createTime: '2024-01-19 11:30:00',
    updateTime: '2024-01-23 16:20:00'
  },
  {
    id: 'server-7',
    name: '库存管理系统',
    description: '商品库存查询、预占、释放等功能',
    url: 'https://inventory.warehouse.com/mcp',
    authType: 'basic',
    authConfig: {
      username: 'inventory_api',
      password: 'inv_secret_2024'
    },
    status: 'active',
    createTime: '2024-01-20 08:15:00',
    updateTime: '2024-01-23 12:10:00'
  },
  {
    id: 'server-8',
    name: '地理位置服务',
    description: '地址解析、距离计算、路径规划',
    url: 'https://maps.geo-service.com/mcp',
    authType: 'api-key',
    authConfig: {
      apiKey: 'geo-key-456789',
      headerName: 'X-GEO-KEY'
    },
    status: 'inactive',
    createTime: '2024-01-21 13:45:00',
    updateTime: '2024-01-23 10:30:00'
  }
]

export const mockMcpTools: McpTool[] = [
  // 业务系统A的工具
  {
    id: 'tool-1',
    serverId: 'server-1',
    serverName: '业务系统A',
    name: 'getUserInfo',
    displayName: '获取用户信息',
    description: '根据用户ID获取用户的详细信息',
    category: 'data',
    tags: ['用户', '查询'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          description: '用户ID'
        },
        includeProfile: {
          type: 'boolean',
          description: '是否包含详细档案',
          default: true
        }
      },
      required: ['userId']
    },
    outputSchema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        email: { type: 'string' },
        phone: { type: 'string' },
        profile: { type: 'object' }
      }
    },
    createTime: '2024-01-15 10:35:00',
    updateTime: '2024-01-20 14:25:00'
  },
  {
    id: 'tool-2',
    serverId: 'server-1',
    serverName: '业务系统A',
    name: 'searchOrders',
    displayName: '查询订单',
    description: '根据条件搜索订单信息',
    category: 'data',
    tags: ['订单', '搜索'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        userId: { type: 'string', description: '用户ID' },
        status: { type: 'string', description: '订单状态' },
        startDate: { type: 'string', description: '开始日期' },
        endDate: { type: 'string', description: '结束日期' },
        limit: { type: 'number', description: '查询数量限制', default: 10 }
      }
    },
    outputSchema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        orders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              userId: { type: 'string' },
              amount: { type: 'number' },
              status: { type: 'string' },
              createTime: { type: 'string' }
            }
          }
        }
      }
    },
    createTime: '2024-01-15 10:40:00',
    updateTime: '2024-01-20 14:30:00'
  },
  // 支付服务的工具
  {
    id: 'tool-3',
    serverId: 'server-2',
    serverName: '第三方支付服务',
    name: 'processPayment',
    displayName: '处理支付',
    description: '处理用户支付请求',
    category: 'api',
    tags: ['支付', '交易'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        orderId: { type: 'string', description: '订单ID' },
        amount: { type: 'number', description: '支付金额' },
        paymentMethod: { type: 'string', description: '支付方式' },
        currency: { type: 'string', description: '货币类型', default: 'CNY' }
      },
      required: ['orderId', 'amount', 'paymentMethod']
    },
    outputSchema: {
      type: 'object',
      properties: {
        paymentId: { type: 'string' },
        status: { type: 'string' },
        transactionId: { type: 'string' },
        paymentUrl: { type: 'string' }
      }
    },
    createTime: '2024-01-16 09:20:00',
    updateTime: '2024-01-18 16:50:00'
  },
  {
    id: 'tool-4',
    serverId: 'server-2',
    serverName: '第三方支付服务',
    name: 'refundPayment',
    displayName: '退款处理',
    description: '处理订单退款请求',
    category: 'api',
    tags: ['退款', '交易'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        paymentId: { type: 'string', description: '支付ID' },
        refundAmount: { type: 'number', description: '退款金额' },
        reason: { type: 'string', description: '退款原因' }
      },
      required: ['paymentId', 'refundAmount']
    },
    outputSchema: {
      type: 'object',
      properties: {
        refundId: { type: 'string' },
        status: { type: 'string' },
        estimatedTime: { type: 'string' }
      }
    },
    createTime: '2024-01-16 09:25:00',
    updateTime: '2024-01-18 16:55:00'
  },
  // 数据分析平台的工具
  {
    id: 'tool-5',
    serverId: 'server-3',
    serverName: '数据分析平台',
    name: 'generateReport',
    displayName: '生成报表',
    description: '根据条件生成数据分析报表',
    category: 'utility',
    tags: ['报表', '分析'],
    status: 'inactive',
    inputSchema: {
      type: 'object',
      properties: {
        reportType: { type: 'string', description: '报表类型' },
        dateRange: { type: 'object', description: '日期范围' },
        filters: { type: 'object', description: '过滤条件' },
        format: { type: 'string', description: '输出格式', default: 'json' }
      },
      required: ['reportType', 'dateRange']
    },
    outputSchema: {
      type: 'object',
      properties: {
        reportId: { type: 'string' },
        downloadUrl: { type: 'string' },
        summary: { type: 'object' }
      }
    },
    createTime: '2024-01-10 11:05:00',
    updateTime: '2024-01-22 13:35:00'
  },
  // 文件存储服务的工具
  {
    id: 'tool-6',
    serverId: 'server-4',
    serverName: '文件存储服务',
    name: 'uploadFile',
    displayName: '上传文件',
    description: '上传文件到存储服务',
    category: 'file',
    tags: ['文件', '上传'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        fileName: { type: 'string', description: '文件名' },
        fileType: { type: 'string', description: '文件类型' },
        fileSize: { type: 'number', description: '文件大小(字节)' },
        folder: { type: 'string', description: '存储文件夹', default: 'default' }
      },
      required: ['fileName', 'fileType']
    },
    outputSchema: {
      type: 'object',
      properties: {
        fileId: { type: 'string' },
        uploadUrl: { type: 'string' },
        accessUrl: { type: 'string' }
      }
    },
    createTime: '2024-01-12 15:25:00',
    updateTime: '2024-01-19 10:15:00'
  },
  // 智能推荐引擎的工具
  {
    id: 'tool-7',
    serverId: 'server-5',
    serverName: '智能推荐引擎',
    name: 'getRecommendations',
    displayName: '获取推荐内容',
    description: '基于用户行为获取个性化推荐',
    category: 'ai',
    tags: ['推荐', '个性化', 'AI'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        userId: { type: 'string', description: '用户ID' },
        category: { type: 'string', description: '推荐类别' },
        limit: { type: 'number', description: '推荐数量', default: 10 },
        scene: { type: 'string', description: '应用场景', default: 'homepage' }
      },
      required: ['userId']
    },
    outputSchema: {
      type: 'object',
      properties: {
        recommendations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              title: { type: 'string' },
              score: { type: 'number' },
              reason: { type: 'string' }
            }
          }
        },
        total: { type: 'number' }
      }
    },
    createTime: '2024-01-18 14:05:00',
    updateTime: '2024-01-23 09:50:00'
  },
  {
    id: 'tool-8',
    serverId: 'server-5',
    serverName: '智能推荐引擎',
    name: 'updateUserProfile',
    displayName: '更新用户画像',
    description: '更新用户行为数据和偏好标签',
    category: 'ai',
    tags: ['用户画像', '行为分析'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        userId: { type: 'string', description: '用户ID' },
        behaviors: { type: 'array', description: '行为数据列表' },
        preferences: { type: 'object', description: '偏好设置' },
        tags: { type: 'array', description: '用户标签' }
      },
      required: ['userId', 'behaviors']
    },
    outputSchema: {
      type: 'object',
      properties: {
        status: { type: 'string' },
        updatedFields: { type: 'array' },
        profileScore: { type: 'number' }
      }
    },
    createTime: '2024-01-18 14:10:00',
    updateTime: '2024-01-23 09:55:00'
  },
  // 消息通知中心的工具
  {
    id: 'tool-9',
    serverId: 'server-6',
    serverName: '消息通知中心',
    name: 'sendNotification',
    displayName: '发送通知',
    description: '发送多渠道消息通知',
    category: 'communication',
    tags: ['通知', '消息', '推送'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        recipients: { type: 'array', description: '接收者列表' },
        title: { type: 'string', description: '消息标题' },
        content: { type: 'string', description: '消息内容' },
        channels: { type: 'array', description: '推送渠道', default: ['email'] },
        priority: { type: 'string', description: '优先级', default: 'normal' }
      },
      required: ['recipients', 'title', 'content']
    },
    outputSchema: {
      type: 'object',
      properties: {
        messageId: { type: 'string' },
        status: { type: 'string' },
        sentCount: { type: 'number' },
        failedCount: { type: 'number' }
      }
    },
    createTime: '2024-01-19 11:35:00',
    updateTime: '2024-01-23 16:25:00'
  },
  {
    id: 'tool-10',
    serverId: 'server-6',
    serverName: '消息通知中心',
    name: 'getNotificationStatus',
    displayName: '查询通知状态',
    description: '查询消息发送状态和统计信息',
    category: 'communication',
    tags: ['状态查询', '统计'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        messageId: { type: 'string', description: '消息ID' },
        dateRange: { type: 'object', description: '查询时间范围' }
      },
      required: ['messageId']
    },
    outputSchema: {
      type: 'object',
      properties: {
        messageId: { type: 'string' },
        status: { type: 'string' },
        sendTime: { type: 'string' },
        deliveryStats: { type: 'object' }
      }
    },
    createTime: '2024-01-19 11:40:00',
    updateTime: '2024-01-23 16:30:00'
  },
  // 库存管理系统的工具
  {
    id: 'tool-11',
    serverId: 'server-7',
    serverName: '库存管理系统',
    name: 'checkInventory',
    displayName: '查询库存',
    description: '查询商品库存数量和状态',
    category: 'data',
    tags: ['库存', '查询'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        skuIds: { type: 'array', description: 'SKU列表' },
        warehouseId: { type: 'string', description: '仓库ID' },
        includeReserved: { type: 'boolean', description: '是否包含预占库存', default: false }
      },
      required: ['skuIds']
    },
    outputSchema: {
      type: 'object',
      properties: {
        inventory: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              skuId: { type: 'string' },
              available: { type: 'number' },
              reserved: { type: 'number' },
              total: { type: 'number' }
            }
          }
        }
      }
    },
    createTime: '2024-01-20 08:20:00',
    updateTime: '2024-01-23 12:15:00'
  },
  {
    id: 'tool-12',
    serverId: 'server-7',
    serverName: '库存管理系统',
    name: 'reserveInventory',
    displayName: '预占库存',
    description: '为订单预占商品库存',
    category: 'api',
    tags: ['库存', '预占'],
    status: 'active',
    inputSchema: {
      type: 'object',
      properties: {
        orderId: { type: 'string', description: '订单ID' },
        items: { type: 'array', description: '商品列表' },
        expireTime: { type: 'string', description: '过期时间' }
      },
      required: ['orderId', 'items']
    },
    outputSchema: {
      type: 'object',
      properties: {
        reservationId: { type: 'string' },
        status: { type: 'string' },
        expireTime: { type: 'string' },
        reservedItems: { type: 'array' }
      }
    },
    createTime: '2024-01-20 08:25:00',
    updateTime: '2024-01-23 12:20:00'
  },
  // 地理位置服务的工具
  {
    id: 'tool-13',
    serverId: 'server-8',
    serverName: '地理位置服务',
    name: 'geocodeAddress',
    displayName: '地址解析',
    description: '将地址转换为经纬度坐标',
    category: 'utility',
    tags: ['地理', '地址', '坐标'],
    status: 'inactive',
    inputSchema: {
      type: 'object',
      properties: {
        address: { type: 'string', description: '地址字符串' },
        city: { type: 'string', description: '城市' },
        country: { type: 'string', description: '国家', default: 'CN' }
      },
      required: ['address']
    },
    outputSchema: {
      type: 'object',
      properties: {
        latitude: { type: 'number' },
        longitude: { type: 'number' },
        formattedAddress: { type: 'string' },
        accuracy: { type: 'string' }
      }
    },
    createTime: '2024-01-21 13:50:00',
    updateTime: '2024-01-23 10:35:00'
  },
  {
    id: 'tool-14',
    serverId: 'server-8',
    serverName: '地理位置服务',
    name: 'calculateDistance',
    displayName: '距离计算',
    description: '计算两点之间的距离',
    category: 'utility',
    tags: ['地理', '距离', '计算'],
    status: 'inactive',
    inputSchema: {
      type: 'object',
      properties: {
        origin: { type: 'object', description: '起点坐标' },
        destination: { type: 'object', description: '终点坐标' },
        unit: { type: 'string', description: '距离单位', default: 'km' }
      },
      required: ['origin', 'destination']
    },
    outputSchema: {
      type: 'object',
      properties: {
        distance: { type: 'number' },
        unit: { type: 'string' },
        estimatedTime: { type: 'number' }
      }
    },
    createTime: '2024-01-21 13:55:00',
    updateTime: '2024-01-23 10:40:00'
  }
]

export const mockRobotMcpConfigs: Record<string, RobotMcpConfig> = {
  'robot-1': {
    id: 'config-1',
    robotId: 'robot-1',
    enabledTools: ['tool-1', 'tool-2', 'tool-6', 'tool-7', 'tool-9', 'tool-11'],
    toolConfigs: {
      'tool-1': { enabled: true, priority: 90, timeout: 30, retryCount: 3 },
      'tool-2': { enabled: true, priority: 80, timeout: 45, retryCount: 2 },
      'tool-6': { enabled: true, priority: 60, timeout: 60, retryCount: 1 },
      'tool-7': { enabled: true, priority: 85, timeout: 40, retryCount: 2 },
      'tool-9': { enabled: true, priority: 70, timeout: 50, retryCount: 3 },
      'tool-11': { enabled: true, priority: 75, timeout: 35, retryCount: 2 }
    },
    maxToolCalls: 8,
    timeout: 120,
    createTime: '2024-01-20 10:00:00',
    updateTime: '2024-01-23 15:30:00'
  },
  'robot-2': {
    id: 'config-2',
    robotId: 'robot-2',
    enabledTools: ['tool-3', 'tool-4', 'tool-12'],
    toolConfigs: {
      'tool-3': { enabled: true, priority: 95, timeout: 60, retryCount: 3 },
      'tool-4': { enabled: true, priority: 90, timeout: 45, retryCount: 2 },
      'tool-12': { enabled: true, priority: 80, timeout: 40, retryCount: 2 }
    },
    maxToolCalls: 3,
    timeout: 90,
    createTime: '2024-01-21 14:00:00',
    updateTime: '2024-01-23 16:00:00'
  },
  'robot-3': {
    id: 'config-3',
    robotId: 'robot-3',
    enabledTools: ['tool-8', 'tool-10', 'tool-13', 'tool-14'],
    toolConfigs: {
      'tool-8': { enabled: true, priority: 85, timeout: 50, retryCount: 2 },
      'tool-10': { enabled: true, priority: 75, timeout: 30, retryCount: 3 },
      'tool-13': { enabled: false, priority: 60, timeout: 40, retryCount: 1 },
      'tool-14': { enabled: false, priority: 65, timeout: 35, retryCount: 1 }
    },
    maxToolCalls: 4,
    timeout: 80,
    createTime: '2024-01-22 11:30:00',
    updateTime: '2024-01-23 12:45:00'
  }
}

export const mockMcpExecutionLogs = [
  {
    id: 'log-1',
    toolId: 'tool-1',
    toolName: '获取用户信息',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 245,
    inputParams: { userId: 'user123', includeProfile: true },
    output: {
      id: 'user123',
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      profile: { age: 28, city: '北京' }
    },
    startTime: '2024-01-22 14:30:15',
    endTime: '2024-01-22 14:30:15',
    executeTime: '2024-01-22 14:30:15'
  },
  {
    id: 'log-2',
    toolId: 'tool-2',
    toolName: '查询订单',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 567,
    inputParams: { userId: 'user123', status: 'pending', limit: 5 },
    output: {
      total: 2,
      orders: [
        { id: 'order-1', userId: 'user123', amount: 299.00, status: 'pending', createTime: '2024-01-22 10:00:00' },
        { id: 'order-2', userId: 'user123', amount: 159.00, status: 'pending', createTime: '2024-01-21 15:30:00' }
      ]
    },
    startTime: '2024-01-22 14:32:20',
    endTime: '2024-01-22 14:32:21',
    executeTime: '2024-01-22 14:32:20'
  },
  {
    id: 'log-3',
    toolId: 'tool-3',
    toolName: '处理支付',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'failed',
    duration: 5000,
    inputParams: { orderId: 'order-1', amount: 299.00, paymentMethod: 'alipay' },
    errorMessage: '支付网关超时',
    stackTrace: 'PaymentGatewayTimeoutException: Connection timeout after 5000ms',
    startTime: '2024-01-22 14:35:10',
    endTime: '2024-01-22 14:35:15',
    executeTime: '2024-01-22 14:35:10'
  },
  {
    id: 'log-4',
    toolId: 'tool-6',
    toolName: '上传文件',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'timeout',
    duration: 30000,
    inputParams: { fileName: 'document.pdf', fileType: 'application/pdf', fileSize: 2048576 },
    errorMessage: '上传超时',
    startTime: '2024-01-22 14:40:00',
    endTime: '2024-01-22 14:40:30',
    executeTime: '2024-01-22 14:40:00'
  },
  {
    id: 'log-5',
    toolId: 'tool-7',
    toolName: '获取推荐内容',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 423,
    inputParams: { userId: 'user123', category: 'product', limit: 5, scene: 'chat' },
    output: {
      recommendations: [
        { id: 'prod-1', title: '智能手机X1', score: 0.95, reason: '基于购买历史' },
        { id: 'prod-2', title: '无线耳机Pro', score: 0.87, reason: '用户偏好匹配' },
        { id: 'prod-3', title: '智能手表', score: 0.82, reason: '热门商品' }
      ],
      total: 3
    },
    startTime: '2024-01-23 09:15:20',
    endTime: '2024-01-23 09:15:21',
    executeTime: '2024-01-23 09:15:20'
  },
  {
    id: 'log-6',
    toolId: 'tool-9',
    toolName: '发送通知',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 1200,
    inputParams: { 
      recipients: ['<EMAIL>'], 
      title: '订单状态更新', 
      content: '您的订单已发货',
      channels: ['email', 'sms']
    },
    output: {
      messageId: 'msg-12345',
      status: 'sent',
      sentCount: 2,
      failedCount: 0
    },
    startTime: '2024-01-23 10:20:00',
    endTime: '2024-01-23 10:20:01',
    executeTime: '2024-01-23 10:20:00'
  },
  {
    id: 'log-7',
    toolId: 'tool-11',
    toolName: '查询库存',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 340,
    inputParams: { skuIds: ['sku-001', 'sku-002'], warehouseId: 'wh-beijing' },
    output: {
      inventory: [
        { skuId: 'sku-001', available: 150, reserved: 20, total: 170 },
        { skuId: 'sku-002', available: 89, reserved: 11, total: 100 }
      ]
    },
    startTime: '2024-01-23 11:30:15',
    endTime: '2024-01-23 11:30:15',
    executeTime: '2024-01-23 11:30:15'
  },
  {
    id: 'log-8',
    toolId: 'tool-12',
    toolName: '预占库存',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'failed',
    duration: 800,
    inputParams: { 
      orderId: 'order-123', 
      items: [{ skuId: 'sku-001', quantity: 200 }], 
      expireTime: '2024-01-23 12:00:00' 
    },
    errorMessage: '库存不足',
    stackTrace: 'InsufficientInventoryException: Required: 200, Available: 150',
    startTime: '2024-01-23 11:35:00',
    endTime: '2024-01-23 11:35:01',
    executeTime: '2024-01-23 11:35:00'
  },
  {
    id: 'log-9',
    toolId: 'tool-4',
    toolName: '退款处理',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 2340,
    inputParams: { paymentId: 'pay-67890', refundAmount: 159.00, reason: '用户取消订单' },
    output: {
      refundId: 'refund-123',
      status: 'processing',
      estimatedTime: '3-5个工作日'
    },
    startTime: '2024-01-23 13:10:00',
    endTime: '2024-01-23 13:10:02',
    executeTime: '2024-01-23 13:10:00'
  },
  {
    id: 'log-10',
    toolId: 'tool-8',
    toolName: '更新用户画像',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 670,
    inputParams: { 
      userId: 'user123', 
      behaviors: [
        { action: 'view', category: 'electronics', timestamp: '2024-01-23 14:00:00' },
        { action: 'purchase', category: 'books', timestamp: '2024-01-23 14:05:00' }
      ],
      preferences: { category: 'electronics', priceRange: 'mid' },
      tags: ['tech_enthusiast', 'frequent_buyer']
    },
    output: {
      status: 'updated',
      updatedFields: ['behaviors', 'preferences', 'tags'],
      profileScore: 0.89
    },
    startTime: '2024-01-23 14:15:30',
    endTime: '2024-01-23 14:15:31',
    executeTime: '2024-01-23 14:15:30'
  },
  {
    id: 'log-11',
    toolId: 'tool-10',
    toolName: '查询通知状态',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'success',
    duration: 180,
    inputParams: { messageId: 'msg-12345' },
    output: {
      messageId: 'msg-12345',
      status: 'delivered',
      sendTime: '2024-01-23 10:20:00',
      deliveryStats: {
        email: { sent: 1, delivered: 1, opened: 0 },
        sms: { sent: 1, delivered: 1, replied: 0 }
      }
    },
    startTime: '2024-01-23 15:00:00',
    endTime: '2024-01-23 15:00:00',
    executeTime: '2024-01-23 15:00:00'
  },
  {
    id: 'log-12',
    toolId: 'tool-13',
    toolName: '地址解析',
    robotId: 'robot-1',
    robotName: '客服助手',
    status: 'failed',
    duration: 3000,
    inputParams: { address: '北京市朝阳区三里屯', city: '北京' },
    errorMessage: '地理服务不可用',
    stackTrace: 'GeoServiceUnavailableException: Service temporarily unavailable',
    startTime: '2024-01-23 16:10:00',
    endTime: '2024-01-23 16:10:03',
    executeTime: '2024-01-23 16:10:00'
  }
]
