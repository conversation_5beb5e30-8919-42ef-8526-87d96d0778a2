// A2A协议多智能体协同相关的MOCK数据

// A2A协议配置接口
export interface A2AProtocolConfig {
  id: string
  name: string
  version: string
  description: string
  endpoint: string
  authType: 'token' | 'oauth' | 'basic' | 'none'
  authConfig: Record<string, any>
  timeout: number
  retryCount: number
  status: 'active' | 'inactive' | 'error'
  createdAt: string
  updatedAt: string
  lastUsed?: string
}

// 智能体协同任务
export interface AgentCollaborationTask {
  id: string
  name: string
  description: string
  type: 'sequential' | 'parallel' | 'conditional' | 'loop'
  status: 'draft' | 'active' | 'paused' | 'completed' | 'failed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  participants: AgentParticipant[]
  workflow: WorkflowStep[]
  createdBy: string
  assignedTo: string[]
  startTime?: string
  endTime?: string
  progress: number
  createdAt: string
  updatedAt: string
}

// 参与智能体
export interface AgentParticipant {
  agentId: string
  agentName: string
  role: 'coordinator' | 'executor' | 'monitor' | 'reviewer'
  capabilities: string[]
  status: 'available' | 'busy' | 'offline' | 'error'
  priority: number
  config: Record<string, any>
}

// 工作流步骤
export interface WorkflowStep {
  id: string
  name: string
  type: 'action' | 'decision' | 'parallel' | 'merge'
  agentId: string
  dependencies: string[]
  conditions?: Record<string, any>
  parameters: Record<string, any>
  timeout: number
  retryCount: number
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startTime?: string
  endTime?: string
  result?: any
  error?: string
}

// 智能体通信记录
export interface AgentCommunication {
  id: string
  taskId: string
  fromAgentId: string
  toAgentId: string
  messageType: 'request' | 'response' | 'notification' | 'error'
  protocol: 'A2A' | 'HTTP' | 'WebSocket' | 'gRPC'
  content: Record<string, any>
  status: 'sent' | 'delivered' | 'processed' | 'failed'
  timestamp: string
  responseTime?: number
  error?: string
}

// 协同性能指标
export interface CollaborationMetrics {
  id: string
  taskId: string
  agentId: string
  metrics: {
    responseTime: number
    successRate: number
    errorRate: number
    throughput: number
    availability: number
  }
  timestamp: string
}

// MOCK数据
export const mockA2AProtocolConfigs: A2AProtocolConfig[] = [
  {
    id: 'a2a-001',
    name: '标准A2A协议配置',
    version: '1.0.0',
    description: '基于标准A2A协议的智能体间通信配置',
    endpoint: 'https://api.example.com/a2a/v1',
    authType: 'token',
    authConfig: { token: 'xxx-xxx-xxx', refreshToken: 'yyy-yyy-yyy' },
    timeout: 30000,
    retryCount: 3,
    status: 'active',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
    lastUsed: '2024-01-20T14:25:00Z'
  },
  {
    id: 'a2a-002',
    name: '企业级A2A协议',
    version: '2.1.0',
    description: '支持高并发和负载均衡的企业级A2A协议配置',
    endpoint: 'https://enterprise.example.com/a2a/v2',
    authType: 'oauth',
    authConfig: { 
      clientId: 'enterprise-client',
      clientSecret: 'secret-key',
      scope: 'agent:communicate'
    },
    timeout: 60000,
    retryCount: 5,
    status: 'active',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-22T11:45:00Z',
    lastUsed: '2024-01-22T11:20:00Z'
  },
  {
    id: 'a2a-003',
    name: '测试环境A2A协议',
    version: '1.5.0',
    description: '用于开发和测试的A2A协议配置',
    endpoint: 'https://test.example.com/a2a/v1',
    authType: 'basic',
    authConfig: { username: 'testuser', password: 'testpass' },
    timeout: 15000,
    retryCount: 2,
    status: 'inactive',
    createdAt: '2024-01-05T14:00:00Z',
    updatedAt: '2024-01-18T16:20:00Z'
  },
  {
    id: 'a2a-004',
    name: '内网A2A协议',
    version: '1.0.0',
    description: '内网环境下的A2A协议配置，无需认证',
    endpoint: 'http://internal.example.com:8080/a2a',
    authType: 'none',
    authConfig: {},
    timeout: 10000,
    retryCount: 1,
    status: 'error',
    createdAt: '2024-01-12T08:00:00Z',
    updatedAt: '2024-01-21T10:15:00Z'
  }
]

export const mockAgentCollaborationTasks: AgentCollaborationTask[] = [
  {
    id: 'task-001',
    name: '客户服务智能体协同',
    description: '多个智能体协同处理客户咨询，包括意图识别、知识查询、回复生成等',
    type: 'sequential',
    status: 'active',
    priority: 'high',
    participants: [
      {
        agentId: 'agent-nlp-001',
        agentName: 'NLP理解智能体',
        role: 'coordinator',
        capabilities: ['意图识别', '实体提取', '情感分析'],
        status: 'available',
        priority: 1,
        config: { model: 'bert-large', threshold: 0.8 }
      },
      {
        agentId: 'agent-kb-001',
        agentName: '知识库查询智能体',
        role: 'executor',
        capabilities: ['知识检索', '语义匹配', '答案排序'],
        status: 'available',
        priority: 2,
        config: { index: 'customer-kb', topK: 5 }
      },
      {
        agentId: 'agent-gen-001',
        agentName: '回复生成智能体',
        role: 'executor',
        capabilities: ['文本生成', '风格控制', '多语言支持'],
        status: 'busy',
        priority: 3,
        config: { model: 'gpt-3.5-turbo', maxTokens: 512 }
      }
    ],
    workflow: [
      {
        id: 'step-001',
        name: '用户输入理解',
        type: 'action',
        agentId: 'agent-nlp-001',
        dependencies: [],
        parameters: { input: 'user_query', output: 'intent_entities' },
        timeout: 5000,
        retryCount: 2,
        status: 'completed',
        startTime: '2024-01-22T10:00:00Z',
        endTime: '2024-01-22T10:00:03Z',
        result: { intent: 'product_inquiry', entities: ['iPhone 15', 'price'] }
      },
      {
        id: 'step-002',
        name: '知识库查询',
        type: 'action',
        agentId: 'agent-kb-001',
        dependencies: ['step-001'],
        parameters: { query: 'intent_entities', output: 'knowledge_results' },
        timeout: 8000,
        retryCount: 3,
        status: 'completed',
        startTime: '2024-01-22T10:00:03Z',
        endTime: '2024-01-22T10:00:08Z',
        result: { documents: ['doc1', 'doc2'], confidence: 0.95 }
      },
      {
        id: 'step-003',
        name: '回复生成',
        type: 'action',
        agentId: 'agent-gen-001',
        dependencies: ['step-002'],
        parameters: { context: 'knowledge_results', output: 'response' },
        timeout: 10000,
        retryCount: 2,
        status: 'running',
        startTime: '2024-01-22T10:00:08Z'
      }
    ],
    createdBy: 'admin',
    assignedTo: ['team-customer-service'],
    startTime: '2024-01-22T10:00:00Z',
    progress: 75,
    createdAt: '2024-01-20T09:00:00Z',
    updatedAt: '2024-01-22T10:00:08Z'
  },
  {
    id: 'task-002',
    name: '内容审核智能体协同',
    description: '多智能体并行处理内容审核，包括文本、图像、视频等多模态内容',
    type: 'parallel',
    status: 'completed',
    priority: 'medium',
    participants: [
      {
        agentId: 'agent-text-001',
        agentName: '文本审核智能体',
        role: 'executor',
        capabilities: ['敏感词检测', '情感分析', '垃圾内容识别'],
        status: 'available',
        priority: 1,
        config: { sensitivity: 'high', language: 'zh-CN' }
      },
      {
        agentId: 'agent-image-001',
        agentName: '图像审核智能体',
        role: 'executor',
        capabilities: ['图像分类', '不当内容检测', 'OCR文字识别'],
        status: 'available',
        priority: 1,
        config: { model: 'resnet-50', threshold: 0.7 }
      },
      {
        agentId: 'agent-video-001',
        agentName: '视频审核智能体',
        role: 'executor',
        capabilities: ['关键帧提取', '音频分析', '动作识别'],
        status: 'offline',
        priority: 1,
        config: { frameRate: 1, audioCheck: true }
      },
      {
        agentId: 'agent-merge-001',
        agentName: '结果合并智能体',
        role: 'reviewer',
        capabilities: ['结果聚合', '决策融合', '报告生成'],
        status: 'available',
        priority: 2,
        config: { strategy: 'majority_vote', weights: [0.4, 0.3, 0.3] }
      }
    ],
    workflow: [
      {
        id: 'step-101',
        name: '文本内容审核',
        type: 'action',
        agentId: 'agent-text-001',
        dependencies: [],
        parameters: { content: 'text_content', output: 'text_result' },
        timeout: 3000,
        retryCount: 2,
        status: 'completed',
        startTime: '2024-01-21T14:00:00Z',
        endTime: '2024-01-21T14:00:02Z',
        result: { safe: true, confidence: 0.92, flags: [] }
      },
      {
        id: 'step-102',
        name: '图像内容审核',
        type: 'action',
        agentId: 'agent-image-001',
        dependencies: [],
        parameters: { content: 'image_content', output: 'image_result' },
        timeout: 8000,
        retryCount: 2,
        status: 'completed',
        startTime: '2024-01-21T14:00:00Z',
        endTime: '2024-01-21T14:00:05Z',
        result: { safe: true, confidence: 0.88, objects: ['person', 'building'] }
      },
      {
        id: 'step-103',
        name: '视频内容审核',
        type: 'action',
        agentId: 'agent-video-001',
        dependencies: [],
        parameters: { content: 'video_content', output: 'video_result' },
        timeout: 30000,
        retryCount: 1,
        status: 'failed',
        startTime: '2024-01-21T14:00:00Z',
        endTime: '2024-01-21T14:00:15Z',
        error: 'Agent offline'
      },
      {
        id: 'step-104',
        name: '审核结果合并',
        type: 'merge',
        agentId: 'agent-merge-001',
        dependencies: ['step-101', 'step-102', 'step-103'],
        parameters: { results: 'all_results', output: 'final_decision' },
        timeout: 2000,
        retryCount: 1,
        status: 'completed',
        startTime: '2024-01-21T14:00:15Z',
        endTime: '2024-01-21T14:00:16Z',
        result: { decision: 'approved', confidence: 0.90, reason: 'Text and image passed' }
      }
    ],
    createdBy: 'content-admin',
    assignedTo: ['team-content-moderation'],
    startTime: '2024-01-21T14:00:00Z',
    endTime: '2024-01-21T14:00:16Z',
    progress: 100,
    createdAt: '2024-01-21T13:30:00Z',
    updatedAt: '2024-01-21T14:00:16Z'
  },
  {
    id: 'task-003',
    name: '智能推荐系统协同',
    description: '多智能体协同生成个性化推荐，包括用户画像、物品特征、推荐算法等',
    type: 'conditional',
    status: 'paused',
    priority: 'low',
    participants: [
      {
        agentId: 'agent-profile-001',
        agentName: '用户画像智能体',
        role: 'coordinator',
        capabilities: ['行为分析', '偏好建模', '标签生成'],
        status: 'available',
        priority: 1,
        config: { lookbackDays: 30, minInteractions: 10 }
      },
      {
        agentId: 'agent-item-001',
        agentName: '物品特征智能体',
        role: 'executor',
        capabilities: ['特征提取', '相似度计算', '分类标注'],
        status: 'busy',
        priority: 2,
        config: { embedding: 'word2vec', dimension: 128 }
      },
      {
        agentId: 'agent-rec-001',
        agentName: '推荐算法智能体',
        role: 'executor',
        capabilities: ['协同过滤', '深度学习', '实时推荐'],
        status: 'available',
        priority: 3,
        config: { algorithm: 'neural_cf', batchSize: 256 }
      }
    ],
    workflow: [
      {
        id: 'step-201',
        name: '用户画像构建',
        type: 'action',
        agentId: 'agent-profile-001',
        dependencies: [],
        parameters: { userId: 'user_id', output: 'user_profile' },
        timeout: 5000,
        retryCount: 2,
        status: 'completed',
        startTime: '2024-01-20T16:00:00Z',
        endTime: '2024-01-20T16:00:04Z',
        result: { interests: ['tech', 'sports'], demographics: { age: '25-34', gender: 'M' } }
      },
      {
        id: 'step-202',
        name: '推荐策略选择',
        type: 'decision',
        agentId: 'agent-rec-001',
        dependencies: ['step-201'],
        conditions: { 
          'user_profile.newUser': 'step-203',
          'user_profile.activeUser': 'step-204',
          'default': 'step-205'
        },
        parameters: { profile: 'user_profile', output: 'strategy' },
        timeout: 1000,
        retryCount: 1,
        status: 'pending'
      }
    ],
    createdBy: 'rec-admin',
    assignedTo: ['team-recommendation'],
    progress: 25,
    createdAt: '2024-01-20T15:30:00Z',
    updatedAt: '2024-01-20T16:00:04Z'
  }
]

export const mockAgentCommunications: AgentCommunication[] = [
  {
    id: 'comm-001',
    taskId: 'task-001',
    fromAgentId: 'agent-nlp-001',
    toAgentId: 'agent-kb-001',
    messageType: 'request',
    protocol: 'A2A',
    content: {
      action: 'search_knowledge',
      parameters: {
        intent: 'product_inquiry',
        entities: ['iPhone 15', 'price'],
        context: 'customer_service'
      }
    },
    status: 'processed',
    timestamp: '2024-01-22T10:00:03Z',
    responseTime: 150
  },
  {
    id: 'comm-002',
    taskId: 'task-001',
    fromAgentId: 'agent-kb-001',
    toAgentId: 'agent-nlp-001',
    messageType: 'response',
    protocol: 'A2A',
    content: {
      status: 'success',
      data: {
        documents: [
          { id: 'doc1', title: 'iPhone 15 价格信息', content: 'iPhone 15 售价...' },
          { id: 'doc2', title: 'iPhone 15 规格参数', content: '规格包括...' }
        ],
        confidence: 0.95
      }
    },
    status: 'delivered',
    timestamp: '2024-01-22T10:00:08Z',
    responseTime: 5000
  },
  {
    id: 'comm-003',
    taskId: 'task-002',
    fromAgentId: 'agent-text-001',
    toAgentId: 'agent-merge-001',
    messageType: 'notification',
    protocol: 'A2A',
    content: {
      event: 'task_completed',
      data: {
        stepId: 'step-101',
        result: { safe: true, confidence: 0.92, flags: [] }
      }
    },
    status: 'processed',
    timestamp: '2024-01-21T14:00:02Z',
    responseTime: 50
  },
  {
    id: 'comm-004',
    taskId: 'task-002',
    fromAgentId: 'agent-video-001',
    toAgentId: 'agent-merge-001',
    messageType: 'error',
    protocol: 'A2A',
    content: {
      error: 'agent_offline',
      message: 'Video processing agent is currently offline',
      code: 'A2A_AGENT_UNAVAILABLE'
    },
    status: 'failed',
    timestamp: '2024-01-21T14:00:15Z',
    error: 'Connection timeout'
  }
]

export const mockCollaborationMetrics: CollaborationMetrics[] = [
  {
    id: 'metrics-001',
    taskId: 'task-001',
    agentId: 'agent-nlp-001',
    metrics: {
      responseTime: 2500,
      successRate: 0.98,
      errorRate: 0.02,
      throughput: 150,
      availability: 0.99
    },
    timestamp: '2024-01-22T10:00:00Z'
  },
  {
    id: 'metrics-002',
    taskId: 'task-001',
    agentId: 'agent-kb-001',
    metrics: {
      responseTime: 4800,
      successRate: 0.95,
      errorRate: 0.05,
      throughput: 80,
      availability: 0.97
    },
    timestamp: '2024-01-22T10:00:00Z'
  },
  {
    id: 'metrics-003',
    taskId: 'task-001',
    agentId: 'agent-gen-001',
    metrics: {
      responseTime: 8200,
      successRate: 0.92,
      errorRate: 0.08,
      throughput: 45,
      availability: 0.94
    },
    timestamp: '2024-01-22T10:00:00Z'
  },
  {
    id: 'metrics-004',
    taskId: 'task-002',
    agentId: 'agent-text-001',
    metrics: {
      responseTime: 1800,
      successRate: 0.99,
      errorRate: 0.01,
      throughput: 200,
      availability: 1.0
    },
    timestamp: '2024-01-21T14:00:00Z'
  },
  {
    id: 'metrics-005',
    taskId: 'task-002',
    agentId: 'agent-image-001',
    metrics: {
      responseTime: 5200,
      successRate: 0.96,
      errorRate: 0.04,
      throughput: 60,
      availability: 0.98
    },
    timestamp: '2024-01-21T14:00:00Z'
  },
  {
    id: 'metrics-006',
    taskId: 'task-002',
    agentId: 'agent-video-001',
    metrics: {
      responseTime: 0,
      successRate: 0.85,
      errorRate: 0.15,
      throughput: 0,
      availability: 0.0
    },
    timestamp: '2024-01-21T14:00:00Z'
  }
]
