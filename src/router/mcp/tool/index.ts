export default [
  {
    path: '/tool',
    name: 'Tool',
    component: () => import('@/views/mcp/tool/index.vue'),
    meta: {
      title: '工具管理',
      icon: 'el-icon-setting',
      breadcrumb: [
        { title: '工具管理', path: '/tool' }
      ]
    }
  },
  {
    path: '/tool/add',
    name: 'ToolAdd',
    component: () => import('@/views/mcp/tool/edit.vue'),
    meta: {
      title: '新增工具',
      icon: 'el-icon-plus',
      breadcrumb: [
        { title: '工具管理', path: '/tool' },
        { title: '新增工具', path: '/tool/add' }
      ]
    }
  },
  {
    path: '/tool/edit/:id',
    name: 'ToolEdit',
    component: () => import('@/views/mcp/tool/edit.vue'),
    meta: {
      title: '编辑工具',
      icon: 'el-icon-edit',
      breadcrumb: [
        { title: '工具管理', path: '/tool' },
        { title: '编辑工具' }
      ]
    }
  },
  {
    path: '/tool/detail/:id',
    name: 'ToolDetail',
    component: () => import('@/views/mcp/tool/edit.vue'),
    meta: {
      title: '工具详情',
      icon: 'el-icon-view',
      breadcrumb: [
        { title: '工具管理', path: '/tool' },
        { title: '工具详情' }
      ]
    }
  }
]