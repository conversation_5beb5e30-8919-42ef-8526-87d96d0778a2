<template>
  <div class="mcp-execution-log">
    <TableToolTemp tool-title="MCP执行日志" :tool-list="toolList" />

    <PageTable 
      ref="pageRef" 
      :search-form-temp="searchFormTemp" 
      :api-function="getMcpExecutionLog"
    >
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column align="center" prop="toolName" label="工具名称" width="150" />
          <el-table-column align="center" prop="robotName" label="智能体" width="120" />
          <el-table-column align="center" prop="status" label="执行状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="duration" label="耗时(ms)" width="100" />
          <el-table-column align="center" prop="inputParams" label="输入参数" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <span class="params-text">{{ formatParams(scope.row.inputParams) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="executeTime" label="执行时间" width="180" />
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="viewDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 执行详情弹窗 -->
    <el-dialog v-model="showDetailDialog" title="执行详情" width="800px">
      <div v-if="currentLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工具名称">{{ currentLog.toolName }}</el-descriptions-item>
          <el-descriptions-item label="智能体">{{ currentLog.robotName }}</el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusTagType(currentLog.status)">
              {{ getStatusText(currentLog.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="耗时">{{ currentLog.duration }}ms</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ currentLog.startTime }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ currentLog.endTime }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>输入参数</h4>
          <el-card shadow="never">
            <pre class="detail-code">{{ formatJson(currentLog.inputParams) }}</pre>
          </el-card>
        </div>

        <div class="detail-section">
          <h4>执行结果</h4>
          <el-card shadow="never">
            <pre class="detail-code" :class="{ 'error-code': currentLog.status === 'failed' }">
              {{ formatJson(currentLog.output || currentLog.errorMessage) }}
            </pre>
          </el-card>
        </div>

        <div v-if="currentLog.errorMessage" class="detail-section">
          <h4>错误信息</h4>
          <el-card shadow="never">
            <pre class="detail-code error-code">{{ currentLog.errorMessage }}</pre>
          </el-card>
        </div>

        <div v-if="currentLog.stackTrace" class="detail-section">
          <h4>堆栈信息</h4>
          <el-card shadow="never">
            <pre class="detail-code error-code">{{ currentLog.stackTrace }}</pre>
          </el-card>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import * as mcpApi from '@/api/mcp'
import { getMcpExecutionLog } from '@/api/mcp'

// 表格引用
const pageRef = ref()

// 弹窗状态
const showDetailDialog = ref(false)
const currentLog = ref<any>()

// 搜索表单配置
const searchFormTemp: SearchFormTemp[] = [
  {
    label: '工具名称',
    name: 'toolName',
    type: 'input',
    placeholder: '请输入工具名称',
    searchState: true
  },
  {
    label: '智能体',
    name: 'robotName',
    type: 'input',
    placeholder: '请输入智能体名称',
    searchState: true
  },
  {
    label: '执行状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    list: [
      { dicItemCode: '', dicItemName: '全部' },
      { dicItemCode: 'success', dicItemName: '成功' },
      { dicItemCode: 'failed', dicItemName: '失败' },
      { dicItemCode: 'timeout', dicItemName: '超时' }
    ],
    searchState: true
  },
  {
    label: '执行时间',
    name: 'dateRange',
    type: 'daterange',
    placeholder: '请选择时间范围',
    searchState: true
  }
]

// 工具栏配置
const toolList: ToolListProps[] = [
  {
    name: '导出日志',
    icon: 'download',
    btnCode: '',
    action: async () => {
      // 导出功能
      console.log('导出日志功能开发中...')
    }
  }
]

// 方法
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'timeout': 'warning',
    'running': 'info'
  }
  return typeMap[status] || ''
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'success': '成功',
    'failed': '失败',
    'timeout': '超时',
    'running': '执行中'
  }
  return textMap[status] || status
}

const formatParams = (params: any) => {
  if (!params) return '-'
  if (typeof params === 'string') return params
  const jsonStr = JSON.stringify(params)
  return jsonStr.length > 50 ? jsonStr.substring(0, 50) + '...' : jsonStr
}

const formatJson = (data: any) => {
  if (!data) return '无数据'
  if (typeof data === 'string') return data
  return JSON.stringify(data, null, 2)
}

const viewDetail = (row: any) => {
  currentLog.value = row
  showDetailDialog.value = true
}
</script>

<style scoped>
.mcp-execution-log {
  padding: 20px;
}

.params-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-detail {
  padding: 10px 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: bold;
}

.detail-code {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.4;
}

.error-code {
  background-color: #fef0f0;
  color: #f56c6c;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

:deep(.el-card__body) {
  padding: 15px;
}

:deep(.el-table__cell) {
  padding: 8px 0;
}
</style> 