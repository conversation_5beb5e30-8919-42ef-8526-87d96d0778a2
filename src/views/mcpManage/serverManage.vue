<template>
  <div class="mcp-server-manage">
    <TableToolTemp tool-title="MCP服务器管理" :tool-list="toolList" />

    <PageTable 
      ref="pageRef" 
      :search-form-temp="searchFormTemp" 
      :api-function="getMcpServerList"
    >
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column align="center" prop="name" label="服务器名称" min-width="150" />
          <el-table-column align="center" prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column align="center" prop="url" label="服务器地址" min-width="200" show-overflow-tooltip />
          <el-table-column align="center" prop="authType" label="认证方式" width="120">
            <template #default="scope">
              <el-tag :type="getAuthTypeTagType(scope.row.authType)">
                {{ getAuthTypeText(scope.row.authType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" width="100">
            <template #default="scope">
              <el-switch 
                v-model="scope.row.status" 
                active-value="active"
                inactive-value="inactive"
                :active-color="system.themeColor"
                @change="onStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="testConnection(scope.row)" :loading="scope.row.testing">
                测试连接
              </el-button>
              <el-button type="text" @click="viewTools(scope.row)">
                查看工具
              </el-button>
              <el-button type="text" @click="editServer(scope.row)">
                编辑
              </el-button>
              <el-popconfirm title="确定删除这个服务器吗？" @confirm="deleteServer(scope.row)">
                <template #reference>
                  <el-button type="text" style="color: #f56c6c;">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 新增/编辑服务器弹窗 -->
    <el-dialog 
      v-model="showServerDialog" 
      :title="dialogTitle" 
      width="600px"
      @closed="resetForm"
    >
      <el-form 
        ref="serverFormRef" 
        :model="serverForm" 
        :rules="serverRules" 
        label-width="100px"
      >
        <el-form-item label="服务器名称" prop="name">
          <el-input v-model="serverForm.name" placeholder="请输入服务器名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="serverForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入服务器描述"
          />
        </el-form-item>
        <el-form-item label="服务器地址" prop="url">
          <el-input v-model="serverForm.url" placeholder="https://example.com/mcp" />
        </el-form-item>
        <el-form-item label="认证方式" prop="authType">
          <el-select v-model="serverForm.authType" @change="onAuthTypeChange">
            <el-option label="无认证" value="none" />
            <el-option label="Basic认证" value="basic" />
            <el-option label="Bearer Token" value="bearer" />
            <el-option label="API Key" value="api-key" />
          </el-select>
        </el-form-item>
        
        <!-- 认证配置 -->
        <div v-if="serverForm.authType !== 'none'">
          <el-form-item 
            v-if="serverForm.authType === 'basic'" 
            label="用户名" 
            prop="authConfig.username"
          >
            <el-input v-model="serverForm.authConfig.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item 
            v-if="serverForm.authType === 'basic'" 
            label="密码" 
            prop="authConfig.password"
          >
            <el-input 
              v-model="serverForm.authConfig.password" 
              type="password" 
              show-password
              placeholder="请输入密码" 
            />
          </el-form-item>
          <el-form-item 
            v-if="serverForm.authType === 'bearer'" 
            label="Token" 
            prop="authConfig.token"
          >
            <el-input 
              v-model="serverForm.authConfig.token" 
              type="textarea"
              placeholder="请输入Bearer Token" 
            />
          </el-form-item>
          <el-form-item 
            v-if="serverForm.authType === 'api-key'" 
            label="API Key" 
            prop="authConfig.apiKey"
          >
            <el-input v-model="serverForm.authConfig.apiKey" placeholder="请输入API Key" />
          </el-form-item>
          <el-form-item 
            v-if="serverForm.authType === 'api-key'" 
            label="Header名称" 
            prop="authConfig.headerName"
          >
            <el-input 
              v-model="serverForm.authConfig.headerName" 
              placeholder="例如: X-API-Key" 
            />
          </el-form-item>
        </div>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showServerDialog = false">取消</el-button>
          <el-button type="primary" @click="saveServer" :loading="saving">
            {{ dialogTitle.includes('新增') ? '新增' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 工具列表弹窗 -->
    <el-dialog v-model="showToolsDialog" title="服务器工具列表" width="800px">
      <div class="tools-header">
        <span>{{ currentServer?.name }} - 可用工具</span>
        <el-button type="primary" size="small" @click="syncTools">
          <el-icon><Refresh /></el-icon>
          同步工具
        </el-button>
      </div>
      
      <el-table :data="serverTools" style="margin-top: 15px;">
        <el-table-column prop="name" label="工具名称" />
        <el-table-column prop="displayName" label="显示名称" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="text" @click="editTool(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import useSystemStore from '@/stores/system'
import * as mcpApi from '@/api/mcp'
import { getMcpServerList } from '@/api/mcp'
import type { McpServer, McpTool } from '@/api/mcp'

const { system } = useSystemStore()

// 表格引用
const pageRef = ref()
const serverFormRef = ref<FormInstance>()

// 弹窗状态
const showServerDialog = ref(false)
const showToolsDialog = ref(false)
const dialogTitle = ref('')
const saving = ref(false)

// 当前选中的服务器
const currentServer = ref<McpServer>()
const serverTools = ref<McpTool[]>([])

// 搜索表单配置
const searchFormTemp: SearchFormTemp[] = [
  {
    label: '服务器名称',
    name: 'name',
    type: 'input',
    placeholder: '请输入服务器名称',
    searchState: true
  },
  {
    label: '状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    list: [
      { dicItemCode: '', dicItemName: '全部' },
      { dicItemCode: 'active', dicItemName: '启用' },
      { dicItemCode: 'inactive', dicItemName: '禁用' }
    ],
    searchState: true
  }
]

// 扩展McpServer类型以包含testing属性
interface ExtendedMcpServer extends McpServer {
  testing?: boolean
}

// 工具栏配置
const toolList: ToolListProps[] = [
  {
    name: '新增服务器',
    icon: 'add',
    btnCode: '',
    action: async () => {
      dialogTitle.value = '新增MCP服务器'
      showServerDialog.value = true
    }
  }
]

// 服务器表单
const serverForm = reactive<McpServer>({
  name: '',
  description: '',
  url: '',
  authType: 'none',
  authConfig: {},
  status: 'active'
})

// 表单验证规则
const serverRules: FormRules = {
  name: [
    { required: true, message: '请输入服务器名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
  ],
  authType: [
    { required: true, message: '请选择认证方式', trigger: 'change' }
  ]
}

// 获取认证方式文本
const getAuthTypeText = (type: string) => {
  const typeMap = {
    'none': '无认证',
    'basic': 'Basic认证',
    'bearer': 'Bearer Token',
    'api-key': 'API Key'
  }
  return typeMap[type] || type
}

// 获取认证方式标签类型
const getAuthTypeTagType = (type: string) => {
  const typeMap = {
    'none': '',
    'basic': 'warning',
    'bearer': 'success',
    'api-key': 'info'
  }
  return typeMap[type] || ''
}

// 认证方式变化处理
const onAuthTypeChange = () => {
  serverForm.authConfig = {}
}

// 状态变化处理
const onStatusChange = async (row: McpServer) => {
  try {
    await mcpApi.updateMcpServer({
      id: row.id,
      status: row.status
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.error('状态更新失败')
  }
}

// 测试连接
const testConnection = async (row: ExtendedMcpServer) => {
  row.testing = true
  try {
    const result = await mcpApi.testMcpConnection({ id: row.id })
    if (result.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败: ' + result.data.message)
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    row.testing = false
  }
}

// 查看工具
const viewTools = async (row: McpServer) => {
  currentServer.value = row
  try {
    const result = await mcpApi.getMcpToolsByServer({ serverId: row.id })
    serverTools.value = result.data || []
    showToolsDialog.value = true
  } catch (error) {
    ElMessage.error('获取工具列表失败')
  }
}

// 同步工具
const syncTools = async () => {
  if (!currentServer.value?.id) return
  
  try {
    await mcpApi.testMcpConnection({ id: currentServer.value.id })
    const result = await mcpApi.getMcpToolsByServer({ serverId: currentServer.value.id })
    serverTools.value = result.data || []
    ElMessage.success('工具同步成功')
  } catch (error) {
    ElMessage.error('工具同步失败')
  }
}

// 编辑服务器
const editServer = async (row: McpServer) => {
  try {
    const result = await mcpApi.getMcpServerDetail({ id: row.id })
    const serverData = result.data
    
    Object.assign(serverForm, {
      id: serverData.id,
      name: serverData.name,
      description: serverData.description,
      url: serverData.url,
      authType: serverData.authType,
      authConfig: serverData.authConfig || {},
      status: serverData.status
    })
    
    dialogTitle.value = '编辑MCP服务器'
    showServerDialog.value = true
  } catch (error) {
    ElMessage.error('获取服务器详情失败')
  }
}

// 删除服务器
const deleteServer = async (row: McpServer) => {
  try {
    await mcpApi.deleteMcpServer({ id: row.id })
    ElMessage.success('删除成功')
    pageRef.value?.onSearchBtn()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 保存服务器
const saveServer = async () => {
  if (!serverFormRef.value) return
  
  try {
    await serverFormRef.value.validate()
    saving.value = true
    
    if (serverForm.id) {
      await mcpApi.updateMcpServer(serverForm)
      ElMessage.success('更新成功')
    } else {
      await mcpApi.addMcpServer(serverForm)
      ElMessage.success('新增成功')
    }
    
    showServerDialog.value = false
    pageRef.value?.onSearchBtn()
  } catch (error) {
    if (error !== false) { // 不是验证错误
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(serverForm, {
    id: undefined,
    name: '',
    description: '',
    url: '',
    authType: 'none',
    authConfig: {},
    status: 'active'
  })
  serverFormRef.value?.clearValidate()
}

// 编辑工具
const editTool = (tool: McpTool) => {
  // 跳转到工具编辑页面或打开编辑弹窗
  ElMessage.info('工具编辑功能开发中...')
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
.mcp-server-manage {
  padding: 20px;
}

.tools-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table__cell) {
  padding: 8px 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style> 