<template>
  <div class="mcp-tool-manage">
    <TableToolTemp tool-title="MCP工具管理" :tool-list="toolList" />

    <PageTable 
      ref="pageRef" 
      :search-form-temp="searchFormTemp" 
      :api-function="getMcpToolList"
    >
      <template #default="{ tableData }">
        <el-table :data="tableData" class="dt-table" stripe>
          <el-table-column align="center" prop="name" label="工具名称" width="150" />
          <el-table-column align="center" prop="displayName" label="显示名称" width="150" />
          <el-table-column align="center" prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column align="center" prop="serverName" label="所属服务器" width="150" />
          <el-table-column align="center" prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag :type="getToolTagType(scope.row.category)">
                {{ scope.row.category }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="状态" width="100">
            <template #default="scope">
              <el-switch 
                v-model="scope.row.status" 
                active-value="active"
                inactive-value="inactive"
                :active-color="system.themeColor"
                @change="onStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="testTool(scope.row)" :loading="scope.row.testing">
                测试
              </el-button>
              <el-button type="text" @click="viewDetail(scope.row)">
                详情
              </el-button>
              <el-button type="text" @click="editTool(scope.row)">
                编辑
              </el-button>
              <el-popconfirm title="确定删除这个工具吗？" @confirm="deleteTool(scope.row)">
                <template #reference>
                  <el-button type="text" style="color: #f56c6c;">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 工具详情弹窗 -->
    <el-dialog v-model="showDetailDialog" title="工具详情" width="700px">
      <div v-if="currentTool" class="tool-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工具名称">{{ currentTool.name }}</el-descriptions-item>
          <el-descriptions-item label="显示名称">{{ currentTool.displayName }}</el-descriptions-item>
          <el-descriptions-item label="所属服务器">{{ currentTool.serverName }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentTool.category }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentTool.status === 'active' ? 'success' : 'info'">
              {{ currentTool.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            <span v-if="currentTool.tags?.length">
              <el-tag 
                v-for="tag in currentTool.tags" 
                :key="tag" 
                size="small"
                style="margin-right: 5px;"
              >
                {{ tag }}
              </el-tag>
            </span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ currentTool.description }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="schema-section">
          <h4>输入参数定义</h4>
          <el-card shadow="never">
            <pre class="schema-code">{{ formatSchema(currentTool.inputSchema) }}</pre>
          </el-card>
        </div>

        <div class="schema-section">
          <h4>输出格式定义</h4>
          <el-card shadow="never">
            <pre class="schema-code">{{ formatSchema(currentTool.outputSchema) }}</pre>
          </el-card>
        </div>
      </div>
    </el-dialog>

    <!-- 测试工具弹窗 -->
    <el-dialog v-model="showTestDialog" title="测试工具" width="600px">
      <div v-if="testingTool">
        <h4>{{ testingTool.displayName || testingTool.name }}</h4>
        <p class="test-description">{{ testingTool.description }}</p>
        
        <el-form :model="testForm" label-width="100px">
          <el-form-item label="测试参数">
            <el-input 
              v-model="testParams" 
              type="textarea" 
              :rows="6"
              placeholder="请输入JSON格式的测试参数"
            />
            <div class="form-tip">请根据输入参数定义填写JSON格式的测试数据</div>
          </el-form-item>
        </el-form>

        <div v-if="testResult" class="test-result">
          <h5>测试结果:</h5>
          <el-card shadow="never">
            <pre class="result-code">{{ testResult }}</pre>
          </el-card>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showTestDialog = false">关闭</el-button>
        <el-button type="primary" @click="runTest" :loading="testing">
          运行测试
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { SearchFormTemp, ToolListProps } from '@/components/types'
import useSystemStore from '@/stores/system'
import * as mcpApi from '@/api/mcp'
import { getMcpToolList } from '@/api/mcp'
import type { McpTool } from '@/api/mcp'

const { system } = useSystemStore()

// 表格引用
const pageRef = ref()

// 弹窗状态
const showDetailDialog = ref(false)
const showTestDialog = ref(false)
const currentTool = ref<McpTool>()
const testingTool = ref<McpTool>()

// 测试相关
const testForm = reactive({})
const testParams = ref('')
const testResult = ref('')
const testing = ref(false)

// 搜索表单配置
const searchFormTemp: SearchFormTemp[] = [
  {
    label: '工具名称',
    name: 'name',
    type: 'input',
    placeholder: '请输入工具名称',
    searchState: true
  },
  {
    label: '所属服务器',
    name: 'serverId',
    type: 'select',
    placeholder: '请选择服务器',
    list: [], // 这里应该动态加载服务器列表
    searchState: true
  },
  {
    label: '分类',
    name: 'category',
    type: 'input',
    placeholder: '请输入分类',
    searchState: true
  },
  {
    label: '状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    list: [
      { dicItemCode: '', dicItemName: '全部' },
      { dicItemCode: 'active', dicItemName: '启用' },
      { dicItemCode: 'inactive', dicItemName: '禁用' }
    ],
    searchState: true
  }
]

// 工具栏配置
const toolList: ToolListProps[] = [
  {
    name: '同步工具',
    icon: 'refresh',
    btnCode: '',
    action: async () => {
      ElMessage.info('工具同步功能开发中...')
    }
  }
]

// 方法
const getToolTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    'data': 'primary',
    'file': 'success', 
    'api': 'warning',
    'utility': 'info',
    'integration': 'danger'
  }
  return typeMap[category] || ''
}

const onStatusChange = async (row: McpTool) => {
  try {
    await mcpApi.updateMcpTool({
      id: row.id,
      status: row.status
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 'active' ? 'inactive' : 'active'
    ElMessage.error('状态更新失败')
  }
}

const testTool = (row: McpTool) => {
  testingTool.value = row
  testParams.value = ''
  testResult.value = ''
  showTestDialog.value = true
}

const runTest = async () => {
  if (!testingTool.value?.id) return
  
  try {
    testing.value = true
    let params = {}
    
    if (testParams.value.trim()) {
      try {
        params = JSON.parse(testParams.value)
      } catch (error) {
        ElMessage.error('测试参数格式错误，请输入有效的JSON')
        return
      }
    }
    
    const result = await mcpApi.testMcpTool({
      toolId: testingTool.value.id,
      params
    })
    
    testResult.value = JSON.stringify(result.data, null, 2)
    ElMessage.success('测试完成')
  } catch (error) {
    ElMessage.error('测试失败')
    testResult.value = '测试失败: ' + error.message
  } finally {
    testing.value = false
  }
}

const viewDetail = (row: McpTool) => {
  currentTool.value = row
  showDetailDialog.value = true
}

const editTool = (row: McpTool) => {
  ElMessage.info('工具编辑功能开发中...')
}

const deleteTool = async (row: McpTool) => {
  try {
    await mcpApi.deleteMcpTool({ id: row.id })
    ElMessage.success('删除成功')
    pageRef.value?.onSearchBtn()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const formatSchema = (schema: any) => {
  if (!schema) return '无定义'
  return JSON.stringify(schema, null, 2)
}
</script>

<style scoped>
.mcp-tool-manage {
  padding: 20px;
}

.tool-detail {
  padding: 10px 0;
}

.schema-section {
  margin-top: 20px;
}

.schema-section h4 {
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: bold;
}

.schema-code {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.test-description {
  color: var(--text-secondary);
  margin-bottom: 15px;
  font-size: 13px;
}

.form-tip {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-top: 5px;
}

.test-result {
  margin-top: 20px;
}

.test-result h5 {
  margin-bottom: 10px;
  color: var(--text-primary);
}

.result-code {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

:deep(.el-card__body) {
  padding: 15px;
}
</style> 