<template>
  <div class="mcp-management-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>MCP (Model Context Protocol) 管理</h1>
      <p>智能体工具配置与服务器管理平台</p>
    </div>

    <div class="mcp-container">
      <!-- 左侧导航菜单 -->
      <div class="mcp-sidebar">
        <div class="nav-section">
          <h3>智能体配置</h3>
          <ul class="nav-list">
            <li 
              v-for="item in botConfigItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>服务器管理</h3>
          <ul class="nav-list">
            <li 
              v-for="item in serverItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>工具管理</h3>
          <ul class="nav-list">
            <li 
              v-for="item in toolItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>执行监控</h3>
          <ul class="nav-list">
            <li 
              v-for="item in monitorItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="mcp-content">
        <div class="content-header">
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>MCP管理</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentTabLabel }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="content-actions">
            <el-button @click="refreshContent" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="openInNewTab">
              <el-icon><Link /></el-icon>
              新窗口打开
            </el-button>
          </div>
        </div>

        <div class="content-body">
          <!-- 智能体配置 -->
          <div v-if="activeTab === 'bot-config'" class="tab-content">
            <botConfig />
          </div>

          <!-- MCP工具配置 -->
          <div v-else-if="activeTab === 'mcp-config'" class="tab-content">
            <mcpConfig />
          </div>

          <!-- 服务器管理 -->
          <div v-else-if="activeTab === 'server-manage'" class="tab-content">
            <serverManage />
          </div>

          <!-- 添加服务器 -->
          <div v-else-if="activeTab === 'server-add'" class="tab-content">
            <serverManage :showAddDialog="true" />
          </div>

          <!-- 工具管理 -->
          <div v-else-if="activeTab === 'tool-manage'" class="tab-content">
            <toolManage />
          </div>

          <!-- 同步工具 -->
          <div v-else-if="activeTab === 'tool-sync'" class="tab-content">
            <toolManage :showSyncDialog="true" />
          </div>

          <!-- 执行日志 -->
          <div v-else-if="activeTab === 'execution-log'" class="tab-content">
            <executionLog />
          </div>

          <!-- 日志导出 -->
          <div v-else-if="activeTab === 'log-export'" class="tab-content">
            <executionLog :showExportDialog="true" />
          </div>

          <!-- 默认欢迎页面 -->
          <div v-else class="welcome-content">
            <el-empty description="请选择左侧菜单查看相应功能">
              <el-button type="primary" @click="selectDefaultTab">
                查看智能体配置
              </el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Robot, 
  Setting, 
  Server, 
  Tools, 
  Document, 
  Refresh, 
  Link,
  Monitor,
  Connection,
  Operation,
  DataAnalysis
} from '@element-plus/icons-vue'

// 导入MCP相关组件
import botConfig from '@/views/botManage/botConfig.vue'
import mcpConfig from '@/views/botManage/components/mcpConfig.vue'
import serverManage from '@/views/mcpManage/serverManage.vue'
import toolManage from '@/views/mcpManage/toolManage.vue'
import executionLog from '@/views/mcpManage/executionLog.vue'

// 导航菜单配置
const botConfigItems = [
  { key: 'bot-config', label: '智能体配置', icon: 'Setting' },
  { key: 'mcp-config', label: 'MCP工具配置', icon: 'Tools' }
]

const serverItems = [
  { key: 'server-manage', label: '服务器管理', icon: 'Server' },
  { key: 'server-add', label: '添加服务器', icon: 'Connection' }
]

const toolItems = [
  { key: 'tool-manage', label: '工具管理', icon: 'Tools' },
  { key: 'tool-sync', label: '同步工具', icon: 'Operation' }
]

const monitorItems = [
  { key: 'execution-log', label: '执行日志', icon: 'Document' },
  { key: 'log-export', label: '日志导出', icon: 'DataAnalysis' }
]

// 响应式数据
const activeTab = ref('')
const refreshing = ref(false)

// 计算属性
const currentTabLabel = computed(() => {
  const allItems = [...botConfigItems, ...serverItems, ...toolItems, ...monitorItems]
  const currentItem = allItems.find(item => item.key === activeTab.value)
  return currentItem ? currentItem.label : '欢迎'
})

// 方法
const selectTab = (key: string) => {
  activeTab.value = key
}

const selectDefaultTab = () => {
  selectTab('bot-config')
}

const refreshContent = () => {
  refreshing.value = true
  // 触发子组件刷新
  setTimeout(() => {
    refreshing.value = false
  }, 1000)
}

const openInNewTab = () => {
  // 根据当前标签页打开对应的独立页面
  const tabRoutes = {
    'bot-config': '/chatbot/botConfig',
    'mcp-config': '/chatbot/botConfig?tab=mcp',
    'server-manage': '/chatbot/mcpServerManage',
    'server-add': '/chatbot/mcpServerManage?action=add',
    'tool-manage': '/chatbot/mcpToolManage',
    'tool-sync': '/chatbot/mcpToolManage?action=sync',
    'execution-log': '/chatbot/mcpExecutionLog',
    'log-export': '/chatbot/mcpExecutionLog?action=export'
  }
  
  const route = tabRoutes[activeTab.value]
  if (route) {
    window.open(route, '_blank')
  }
}

// 生命周期
onMounted(() => {
  // 默认选择智能体配置页面
  selectDefaultTab()
})
</script>

<style lang="less" scoped>
.mcp-management-page {
  height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    p {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }

  .mcp-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .mcp-sidebar {
      width: 280px;
      background: white;
      border-right: 1px solid #e4e7ed;
      overflow-y: auto;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

      .nav-section {
        padding: 20px 0;

        h3 {
          margin: 0 0 15px 20px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          border-left: 3px solid #409eff;
          padding-left: 15px;
        }

        .nav-list {
          list-style: none;
          margin: 0;
          padding: 0;

          li {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;

            &:hover {
              background: #f5f7fa;
              border-left-color: #409eff;
            }

            &.active {
              background: #ecf5ff;
              border-left-color: #409eff;
              color: #409eff;
            }

            .el-icon {
              margin-right: 12px;
              font-size: 18px;
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }

    .mcp-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;

      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #e4e7ed;
        background: #fafafa;

        .breadcrumb {
          .el-breadcrumb {
            font-size: 14px;
          }
        }

        .content-actions {
          display: flex;
          gap: 10px;

          .el-button {
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }
      }

      .content-body {
        flex: 1;
        overflow: auto;
        padding: 20px;

        .tab-content {
          height: 100%;
        }

        .welcome-content {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #fafafa;

          .el-empty {
            text-align: center;

            .el-button {
              margin-top: 20px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .mcp-sidebar {
    width: 240px !important;
  }
}

@media (max-width: 768px) {
  .mcp-container {
    flex-direction: column !important;

    .mcp-sidebar {
      width: 100% !important;
      height: auto !important;
      max-height: 200px;
    }
  }
}
</style>
