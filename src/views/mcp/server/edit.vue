<template>
  <EditPageContainer
    :title="pageTitle"
    :icon="pageIcon"
    :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
    @back="handleBack"
    @save="handleSave"
    @breadcrumb-click="handleBreadcrumbClick"
  >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'120px'"
      :rowGutter="20"
      :isView="isView"
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getServerDetail, getServerDetailByCode, createServer, updateServer } from '@/api/server'

export default {
  name: 'McpServerEdit',
  components: {
    EditPageContainer,
    UniversalForm
  },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      form: {
        serverCode: '',
        serverName: '',
        serverUrl: '',
        serverStatus: 1,
        description: '',
        createTime: '',
        updateTime: '',
        createUser: '',
        updateUser: ''
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-server',
          fields: [
            [
              {
                prop: 'serverName',
                label: '服务器名称',
                type: 'input',
                placeholder: '请输入服务器名称',
                maxlength: 100,
                showWordLimit: true
              },
              {
                prop: 'serverUrl',
                label: '服务器地址',
                type: 'input',
                placeholder: '请输入服务器地址，如：http://localhost:3000',
                maxlength: 500,
                showWordLimit: true
              }
            ],
            [
              {
                prop: 'serverStatus',
                label: '服务器状态',
                type: 'select',
                dicCode: 'gen.useStatus'
              }
            ],
            [
              {
                prop: 'description',
                label: '描述信息',
                type: 'textarea',
                placeholder: '请输入服务器描述信息',
                maxlength: 500,
                rows: 4,
                showWordLimit: true,
                span: 24
              }
            ]
          ]
        }
      ],
      formRules: {
        serverName: [
          { required: true, message: '请输入服务器名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        serverUrl: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
          { 
            pattern: /^(https?:\/\/|wss?:\/\/|tcp:\/\/|udp:\/\/).+/,
            message: '请输入有效的服务器地址（需包含协议前缀）',
            trigger: 'blur'
          }
        ],
        serverStatus: [
          { required: true, message: '请选择服务器状态', trigger: 'change' }
        ],
        description: [
          { max: 500, message: '描述信息不能超过 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) {
        return '查看服务器'
      }
      return this.isEdit ? '编辑服务器' : '新增服务器'
    },
    pageIcon() {
      return 'el-icon-server'
    },
    breadcrumbItems() {
      const items = [
        { text: '服务器管理', to: { name: 'Server' } }
      ]
      
      if (this.isView) {
        items.push({ text: '服务器详情', to: null })
      } else if (this.isEdit) {
        items.push({ text: '编辑服务器', to: null })
      } else {
        items.push({ text: '新增服务器', to: null })
      }
      
      return items
    }
  },
  async created() {
    await this.initPage()
  },
  methods: {
    async initPage() {
      const { id } = this.$route.params
      const { mode } = this.$route.query
      
      // 支持查看模式：当mode=view或路由名称包含detail时进入查看模式
      this.isView = mode === 'view' || this.$route.name === 'ServerDetail'
      this.isEdit = !!id && !this.isView
      
      if (id) {
        await this.loadServerDetail(id)
      }
    },

    async loadServerDetail(id) {
      this.loading = true
      try {
        // 优先尝试通过编码查询，如果失败则通过ID查询
        let response
        try {
          response = await getServerDetailByCode(id)
        } catch (codeError) {
          response = await getServerDetail(id)
        }
        
        if (response) {
          this.form = {
            ...this.form,
            ...response
          }
        } else {
          this.$message.error('加载服务器详情失败')
        }
      } catch (error) {
        console.error('加载服务器详情失败:', error)
        this.$message.error('加载服务器详情失败')
      } finally {
        this.loading = false
      }
    },

    async handleSave() {
      try {
        const valid = await this.$refs.universalForm.validate()
        if (!valid) {
          this.$message.warning('请检查表单数据')
          return
        }

        this.loading = true
        
        const formData = { ...this.form }
        
        if (this.isEdit) {
          await updateServer(formData)
          this.$message.success('更新成功')
        } else {
          await createServer(formData)
          this.$message.success('新增成功')
        }
        
        this.handleBack()
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败，请检查表单数据')
      } finally {
        this.loading = false
      }
    },

    handleBack() {
      // 优先返回到服务器列表页
      const from = this.$route.query.from
      if (from === 'list') {
        this.$router.push({ name: 'McpServer' })
      } else {
        this.$router.go(-1)
      }
    },

    handleBreadcrumbClick(item) {
      // 导航逻辑由BreadcrumbNavigation组件统一处理
    }
  }
}
</script>

<style lang="less" scoped>
// 页面样式由EditPageContainer和UniversalForm组件提供
</style>