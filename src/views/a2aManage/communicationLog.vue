<template>
  <div class="communication-log-page">
    <!-- 工具栏 -->
    <TableToolTemp
      tool-title="智能体通信记录"
      :tool-list="toolList"
    />

    <!-- 统计卡片 -->
    <div v-if="statsData" class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="总通信次数" :value="statsData.total" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="平均响应时间" :value="Math.round(statsData.avgResponseTime)" suffix="ms" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="成功率" :value="getSuccessRate(statsData)" suffix="%" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="A2A协议占比" :value="getA2ARate(statsData)" suffix="%" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <PageTable
      ref="pageRef"
      :search-form-temp="searchFormTemp"
      :api-function="getAgentCommunicationList"
      :table-columns="tableColumns"
      :table-data-handle="tableDataHandle"
      @selection-change="handleSelectionChange"
    >
      <template #default="{ tableData }">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="id" 
            label="记录ID"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="taskId" 
            label="任务ID"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="fromAgentId" 
            label="发送者"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="toAgentId" 
            label="接收者"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="messageType" 
            label="消息类型"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getMessageTypeTag(row.messageType)">
                {{ getMessageTypeName(row.messageType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="protocol" 
            label="协议"
            width="100"
          >
            <template #default="{ row }">
              <el-tag type="info">{{ row.protocol }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="status" 
            label="状态"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="responseTime" 
            label="响应时间"
            width="100"
          >
            <template #default="{ row }">
              {{ row.responseTime ? `${row.responseTime}ms` : '-' }}
            </template>
          </el-table-column>
          <el-table-column 
            prop="timestamp" 
            label="发送时间"
            width="180"
          >
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column 
            label="操作"
            width="150"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="handleViewDetail(row)"
              >
                查看详情
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 通信详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="通信记录详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecord" class="communication-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" header="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="记录ID">{{ currentRecord.id }}</el-descriptions-item>
            <el-descriptions-item label="任务ID">{{ currentRecord.taskId }}</el-descriptions-item>
            <el-descriptions-item label="发送者">{{ currentRecord.fromAgentId }}</el-descriptions-item>
            <el-descriptions-item label="接收者">{{ currentRecord.toAgentId }}</el-descriptions-item>
            <el-descriptions-item label="消息类型">
              <el-tag :type="getMessageTypeTag(currentRecord.messageType)">
                {{ getMessageTypeName(currentRecord.messageType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="协议类型">
              <el-tag :type="getProtocolTag(currentRecord.protocol)">
                {{ currentRecord.protocol }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTag(currentRecord.status)">
                {{ getStatusName(currentRecord.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="响应时间">
              {{ currentRecord.responseTime ? currentRecord.responseTime + 'ms' : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="发送时间" :span="2">
              {{ formatTime(currentRecord.timestamp) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 消息内容 -->
        <el-card class="detail-card" header="消息内容">
          <div class="content-display">
            <pre>{{ JSON.stringify(currentRecord.content, null, 2) }}</pre>
          </div>
        </el-card>

        <!-- 错误信息 -->
        <el-card v-if="currentRecord.error" class="detail-card" header="错误信息">
          <el-alert :title="currentRecord.error" type="error" :closable="false" />
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 统计图表对话框 -->
    <el-dialog
      v-model="chartDialogVisible"
      title="通信统计分析"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="statsData" class="chart-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card header="消息类型分布">
              <div class="chart-placeholder">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="请求消息">{{ statsData.byMessageType.request }}</el-descriptions-item>
                  <el-descriptions-item label="响应消息">{{ statsData.byMessageType.response }}</el-descriptions-item>
                  <el-descriptions-item label="通知消息">{{ statsData.byMessageType.notification }}</el-descriptions-item>
                  <el-descriptions-item label="错误消息">{{ statsData.byMessageType.error }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card header="协议类型分布">
              <div class="chart-placeholder">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="A2A">{{ statsData.byProtocol.A2A }}</el-descriptions-item>
                  <el-descriptions-item label="HTTP">{{ statsData.byProtocol.HTTP }}</el-descriptions-item>
                  <el-descriptions-item label="WebSocket">{{ statsData.byProtocol.WebSocket }}</el-descriptions-item>
                  <el-descriptions-item label="gRPC">{{ statsData.byProtocol.gRPC }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="24">
            <el-card header="状态分布">
              <div class="chart-placeholder">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-statistic title="已发送" :value="statsData.byStatus.sent" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="已送达" :value="statsData.byStatus.delivered" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="已处理" :value="statsData.byStatus.processed" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="失败" :value="statsData.byStatus.failed" />
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="chartDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="refreshStats">刷新数据</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  View, 
  DataAnalysis, 
  Refresh,
  Download
} from '@element-plus/icons-vue'
import TableToolTemp from '@/components/TableToolTemp.vue'
import PageTable from '@/components/PageTable.vue'
import { 
  getAgentCommunicationList,
  getAgentCommunicationDetail,
  getAgentCommunicationStats
} from '@/api/a2a'
import type { AgentCommunication } from '@/mock/a2a'

// 页面引用
const pageRef = ref()

// 对话框状态
const detailDialogVisible = ref(false)
const chartDialogVisible = ref(false)
const currentRecord = ref<AgentCommunication | null>(null)
const statsData = ref(null)

// 工具栏配置
const toolList = [
  {
    name: '统计分析',
    icon: 'DataAnalysis',
    type: 'primary',
    action: () => handleShowStats()
  },
  {
    name: '导出记录',
    icon: 'Download',
    type: 'success',
    action: () => handleExport()
  },
  {
    name: '刷新',
    icon: 'Refresh',
    type: 'info',
    action: () => handleRefresh()
  }
]

// 搜索表单配置
const searchFormTemp = [
  {
    label: '任务ID',
    name: 'taskId',
    type: 'input',
    placeholder: '请输入任务ID'
  },
  {
    label: '发送者',
    name: 'fromAgentId',
    type: 'input',
    placeholder: '请输入发送者ID'
  },
  {
    label: '接收者',
    name: 'toAgentId',
    type: 'input',
    placeholder: '请输入接收者ID'
  },
  {
    label: '消息类型',
    name: 'messageType',
    type: 'select',
    placeholder: '请选择消息类型',
    options: [
      { label: '请求', value: 'request' },
      { label: '响应', value: 'response' },
      { label: '通知', value: 'notification' },
      { label: '错误', value: 'error' }
    ]
  },
  {
    label: '协议类型',
    name: 'protocol',
    type: 'select',
    placeholder: '请选择协议类型',
    options: [
      { label: 'A2A', value: 'A2A' },
      { label: 'HTTP', value: 'HTTP' },
      { label: 'WebSocket', value: 'WebSocket' },
      { label: 'gRPC', value: 'gRPC' }
    ]
  }
]

// 表格列配置
const tableColumns = [
  { type: 'selection', width: 55 },
  { 
    prop: 'id', 
    label: '记录ID',
    width: 120,
    showOverflowTooltip: true
  },
  { 
    prop: 'taskId', 
    label: '任务ID',
    width: 120,
    showOverflowTooltip: true
  },
  { 
    prop: 'fromAgentId', 
    label: '发送者',
    width: 150,
    showOverflowTooltip: true
  },
  { 
    prop: 'toAgentId', 
    label: '接收者',
    width: 150,
    showOverflowTooltip: true
  },
  { 
    prop: 'messageType', 
    label: '消息类型',
    width: 100,
    formatter: (row: any) => getMessageTypeName(row.messageType)
  },
  { 
    prop: 'protocol', 
    label: '协议',
    width: 100
  },
  { 
    prop: 'status', 
    label: '状态',
    width: 100,
    formatter: (row: any) => getStatusName(row.status)
  },
  { 
    prop: 'responseTime', 
    label: '响应时间',
    width: 100,
    formatter: (row: any) => row.responseTime ? `${row.responseTime}ms` : '-'
  },
  { 
    prop: 'timestamp', 
    label: '发送时间',
    width: 180,
    formatter: (row: any) => formatTime(row.timestamp)
  }
]

// 表格数据处理
const tableDataHandle = (data: AgentCommunication[]) => {
  return data.map(item => ({
    ...item,
    actions: [
      {
        name: '查看详情',
        icon: 'View',
        type: 'primary',
        action: () => handleViewDetail(item.id)
      }
    ]
  }))
}

// 方法
const handleViewDetail = async (id: string) => {
  try {
    const result = await getAgentCommunicationDetail(id)
    if (result.status === 200) {
      currentRecord.value = result.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(result.message || '获取通信记录详情失败')
    }
  } catch (error) {
    ElMessage.error('获取通信记录详情失败')
  }
}

const handleShowStats = async () => {
  await loadStats()
  chartDialogVisible.value = true
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleRefresh = () => {
  pageRef.value?.getPageData()
  loadStats()
}

const handleSelectionChange = (selection: AgentCommunication[]) => {
  console.log('Selected items:', selection)
}

const loadStats = async () => {
  try {
    const result = await getAgentCommunicationStats({})
    if (result.status === 200) {
      statsData.value = result.data
    } else {
      ElMessage.error(result.message || '获取统计数据失败')
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const refreshStats = async () => {
  await loadStats()
  ElMessage.success('数据已刷新')
}

// 辅助方法
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

const getMessageTypeName = (type: string) => {
  const typeMap = {
    'request': '请求',
    'response': '响应',
    'notification': '通知',
    'error': '错误'
  }
  return typeMap[type] || type
}

const getMessageTypeTag = (type: string) => {
  const tagMap = {
    'request': 'primary',
    'response': 'success',
    'notification': 'info',
    'error': 'danger'
  }
  return tagMap[type] || ''
}

const getProtocolTag = (protocol: string) => {
  const tagMap = {
    'A2A': 'danger',
    'HTTP': 'primary',
    'WebSocket': 'warning',
    'gRPC': 'success'
  }
  return tagMap[protocol] || ''
}

const getStatusName = (status: string) => {
  const statusMap = {
    'sent': '已发送',
    'delivered': '已送达',
    'processed': '已处理',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap = {
    'sent': 'info',
    'delivered': 'warning',
    'processed': 'success',
    'failed': 'danger'
  }
  return tagMap[status] || ''
}

const getSuccessRate = (stats: any) => {
  if (!stats || !stats.total) return 0
  const successCount = stats.byStatus.delivered + stats.byStatus.processed
  return parseFloat(((successCount / stats.total) * 100).toFixed(1))
}

const getA2ARate = (stats: any) => {
  if (!stats || !stats.total) return 0
  return parseFloat(((stats.byProtocol.A2A / stats.total) * 100).toFixed(1))
}

// 生命周期
onMounted(() => {
  loadStats()
})
</script>

<style lang="less" scoped>
.communication-log-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    text-align: center;
    
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

.communication-detail {
  .detail-card {
    margin-bottom: 20px;
  }

  .content-display {
    pre {
      background: #f5f7fa;
      padding: 16px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.chart-content {
  .chart-placeholder {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
