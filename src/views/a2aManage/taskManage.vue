<template>
  <div class="task-manage-page">
    <!-- 工具栏 -->
    <TableToolTemp
      tool-title="智能体协同任务管理"
      :tool-list="toolList"
    />

    <!-- 数据表格 -->
    <PageTable
      ref="pageRef"
      :search-form-temp="searchFormTemp"
      :api-function="getAgentCollaborationTaskList"
      :table-columns="tableColumns"
      :table-data-handle="tableDataHandle"
      @selection-change="handleSelectionChange"
    >
      <template #default="{ tableData }">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="name" 
            label="任务名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="type" 
            label="任务类型"
            width="120"
          >
            <template #default="{ row }">
              <el-tag :type="getTaskTypeTag(row.type)">
                {{ getTaskTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="status" 
            label="状态"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getTaskStatusTag(row.status)">
                {{ getTaskStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="priority" 
            label="优先级"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getPriorityTag(row.priority)">
                {{ getPriorityName(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="progress" 
            label="进度"
            width="120"
          >
            <template #default="{ row }">
              <el-progress :percentage="row.progress" :stroke-width="8" />
            </template>
          </el-table-column>
          <el-table-column 
            prop="participants" 
            label="参与智能体"
            width="120"
          >
            <template #default="{ row }">
              <el-tag type="info">{{ row.participants?.length || 0 }}个</el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="createdBy" 
            label="创建者"
            width="120"
          />
          <el-table-column 
            prop="updatedAt" 
            label="更新时间"
            width="180"
          >
            <template #default="{ row }">
              {{ formatTime(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column 
            label="操作"
            width="200"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="handleViewDetail(row)"
              >
                查看详情
              </el-button>
              <el-button
                size="small"
                type="warning"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任务详情"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div v-if="currentTask" class="task-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" header="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务名称">{{ currentTask.name }}</el-descriptions-item>
            <el-descriptions-item label="任务类型">
              <el-tag :type="getTaskTypeTag(currentTask.type)">
                {{ getTaskTypeName(currentTask.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="getTaskStatusTag(currentTask.status)">
                {{ getTaskStatusName(currentTask.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityTag(currentTask.priority)">
                {{ getPriorityName(currentTask.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="进度">
              <el-progress :percentage="currentTask.progress" />
            </el-descriptions-item>
            <el-descriptions-item label="创建者">{{ currentTask.createdBy }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ formatTime(currentTask.startTime) }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ formatTime(currentTask.endTime) }}</el-descriptions-item>
          </el-descriptions>
          <div class="description">
            <strong>任务描述：</strong>
            <p>{{ currentTask.description }}</p>
          </div>
        </el-card>

        <!-- 参与智能体 -->
        <el-card class="detail-card" header="参与智能体">
          <el-table :data="currentTask.participants" style="width: 100%">
            <el-table-column prop="agentName" label="智能体名称" />
            <el-table-column prop="role" label="角色">
              <template #default="{ row }">
                <el-tag :type="getRoleTag(row.role)">
                  {{ getRoleName(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getAgentStatusTag(row.status)">
                  {{ getAgentStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="capabilities" label="能力">
              <template #default="{ row }">
                <el-tag
                  v-for="capability in row.capabilities"
                  :key="capability"
                  size="small"
                  style="margin-right: 4px"
                >
                  {{ capability }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80" />
          </el-table>
        </el-card>

        <!-- 工作流步骤 -->
        <el-card class="detail-card" header="工作流步骤">
          <el-timeline>
            <el-timeline-item
              v-for="step in currentTask.workflow"
              :key="step.id"
              :type="getStepTimelineType(step.status)"
              :icon="getStepIcon(step.status)"
            >
              <div class="step-content">
                <div class="step-header">
                  <h4>{{ step.name }}</h4>
                  <el-tag :type="getStepStatusTag(step.status)" size="small">
                    {{ getStepStatusName(step.status) }}
                  </el-tag>
                </div>
                <div class="step-info">
                  <p><strong>执行智能体：</strong>{{ getAgentName(step.agentId) }}</p>
                  <p><strong>步骤类型：</strong>{{ getStepTypeName(step.type) }}</p>
                  <p v-if="step.dependencies?.length"><strong>依赖步骤：</strong>{{ step.dependencies.join(', ') }}</p>
                  <p><strong>超时时间：</strong>{{ step.timeout }}ms</p>
                  <p><strong>重试次数：</strong>{{ step.retryCount }}</p>
                  <p v-if="step.startTime"><strong>开始时间：</strong>{{ formatTime(step.startTime) }}</p>
                  <p v-if="step.endTime"><strong>结束时间：</strong>{{ formatTime(step.endTime) }}</p>
                  <div v-if="step.result" class="step-result">
                    <strong>执行结果：</strong>
                    <pre>{{ JSON.stringify(step.result, null, 2) }}</pre>
                  </div>
                  <div v-if="step.error" class="step-error">
                    <strong>错误信息：</strong>
                    <el-alert :title="step.error" type="error" :closable="false" />
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 性能指标对话框 -->
    <el-dialog
      v-model="metricsDialogVisible"
      title="任务性能指标"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="taskMetrics" class="metrics-content">
        <!-- 总体指标 -->
        <el-card class="metrics-card" header="总体性能">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="平均响应时间" :value="taskMetrics.summary.avgResponseTime" suffix="ms" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均成功率" :value="parseFloat((taskMetrics.summary.avgSuccessRate * 100).toFixed(2))" suffix="%" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="平均可用性" :value="parseFloat((taskMetrics.summary.avgAvailability * 100).toFixed(2))" suffix="%" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总吞吐量" :value="taskMetrics.summary.totalThroughput" suffix="req/min" />
            </el-col>
          </el-row>
        </el-card>

        <!-- 智能体指标详情 -->
        <el-card class="metrics-card" header="智能体性能详情">
          <el-table :data="taskMetrics.metrics" style="width: 100%">
            <el-table-column prop="agentId" label="智能体ID" />
            <el-table-column label="响应时间">
              <template #default="{ row }">
                {{ row.metrics.responseTime }}ms
              </template>
            </el-table-column>
            <el-table-column label="成功率">
              <template #default="{ row }">
                <el-progress 
                  :percentage="row.metrics.successRate * 100" 
                  :color="getSuccessRateColor(row.metrics.successRate)"
                />
              </template>
            </el-table-column>
            <el-table-column label="错误率">
              <template #default="{ row }">
                {{ (row.metrics.errorRate * 100).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column label="吞吐量">
              <template #default="{ row }">
                {{ row.metrics.throughput }} req/min
              </template>
            </el-table-column>
            <el-table-column label="可用性">
              <template #default="{ row }">
                <el-progress 
                  :percentage="row.metrics.availability * 100"
                  :color="getAvailabilityColor(row.metrics.availability)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="metricsDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  View, 
  VideoPlay, 
  VideoPause, 
  Delete,
  DataAnalysis,
  SuccessFilled,
  Loading,
  CircleCloseFilled,
  Warning
} from '@element-plus/icons-vue'
import TableToolTemp from '@/components/TableToolTemp.vue'
import PageTable from '@/components/PageTable.vue'
import { 
  getAgentCollaborationTaskList,
  getAgentCollaborationTaskDetail,
  executeAgentCollaborationTask,
  pauseAgentCollaborationTask,
  deleteAgentCollaborationTask,
  getAgentCollaborationTaskMetrics
} from '@/api/a2a'
import type { AgentCollaborationTask } from '@/mock/a2a'

// 页面引用
const pageRef = ref()

// 对话框状态
const detailDialogVisible = ref(false)
const metricsDialogVisible = ref(false)
const currentTask = ref<AgentCollaborationTask | null>(null)
const taskMetrics = ref(null)

// 工具栏配置
const toolList = [
  {
    name: '新增任务',
    icon: 'Plus',
    type: 'primary',
    action: () => handleAdd()
  }
]

// 搜索表单配置
const searchFormTemp = [
  {
    label: '任务名称',
    name: 'name',
    type: 'input',
    placeholder: '请输入任务名称'
  },
  {
    label: '任务状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '草稿', value: 'draft' },
      { label: '执行中', value: 'active' },
      { label: '暂停', value: 'paused' },
      { label: '已完成', value: 'completed' },
      { label: '失败', value: 'failed' }
    ]
  },
  {
    label: '任务类型',
    name: 'type',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: '顺序执行', value: 'sequential' },
      { label: '并行执行', value: 'parallel' },
      { label: '条件执行', value: 'conditional' },
      { label: '循环执行', value: 'loop' }
    ]
  },
  {
    label: '优先级',
    name: 'priority',
    type: 'select',
    placeholder: '请选择优先级',
    options: [
      { label: '低', value: 'low' },
      { label: '中', value: 'medium' },
      { label: '高', value: 'high' },
      { label: '紧急', value: 'urgent' }
    ]
  }
]

// 表格列配置
const tableColumns = [
  { type: 'selection', width: 55 },
  { 
    prop: 'name', 
    label: '任务名称',
    minWidth: 200,
    showOverflowTooltip: true
  },
  { 
    prop: 'type', 
    label: '任务类型',
    width: 120,
    formatter: (row: any) => getTaskTypeName(row.type)
  },
  { 
    prop: 'status', 
    label: '状态',
    width: 100,
    formatter: (row: any) => getTaskStatusName(row.status)
  },
  { 
    prop: 'priority', 
    label: '优先级',
    width: 100,
    formatter: (row: any) => getPriorityName(row.priority)
  },
  { 
    prop: 'progress', 
    label: '进度',
    width: 120,
    formatter: (row: any) => `${row.progress}%`
  },
  { 
    prop: 'participants', 
    label: '参与智能体',
    width: 120,
    formatter: (row: any) => `${row.participants?.length || 0}个`
  },
  { 
    prop: 'createdBy', 
    label: '创建者',
    width: 120
  },
  { 
    prop: 'updatedAt', 
    label: '更新时间',
    width: 180,
    formatter: (row: any) => formatTime(row.updatedAt)
  }
]

// 表格数据处理
const tableDataHandle = (data: AgentCollaborationTask[]) => {
  return data.map(item => ({
    ...item,
    actions: [
      {
        name: '查看详情',
        icon: 'View',
        type: 'primary',
        action: () => handleViewDetail(item.id)
      },
      {
        name: item.status === 'active' ? '暂停' : '执行',
        icon: item.status === 'active' ? 'VideoPause' : 'VideoPlay',
        type: item.status === 'active' ? 'warning' : 'success',
        action: () => item.status === 'active' ? handlePause(item.id) : handleExecute(item.id),
        disabled: item.status === 'completed' || item.status === 'failed'
      },
      {
        name: '性能指标',
        icon: 'DataAnalysis',
        type: 'info',
        action: () => handleViewMetrics(item.id)
      },
      {
        name: '删除',
        icon: 'Delete',
        type: 'danger',
        action: () => handleDelete(item)
      }
    ]
  }))
}

// 方法
const handleAdd = () => {
  ElMessage.info('任务创建功能开发中...')
}

const handleViewDetail = async (id: string) => {
  try {
    const result = await getAgentCollaborationTaskDetail(id)
    if (result.status === 200) {
      currentTask.value = result.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(result.message || '获取任务详情失败')
    }
  } catch (error) {
    ElMessage.error('获取任务详情失败')
  }
}

const handleExecute = async (id: string) => {
  try {
    const result = await executeAgentCollaborationTask(id)
    if (result.status === 200) {
      ElMessage.success('任务开始执行')
      pageRef.value?.getPageData()
    } else {
      ElMessage.error(result.message || '执行任务失败')
    }
  } catch (error) {
    ElMessage.error('执行任务失败')
  }
}

const handlePause = async (id: string) => {
  try {
    const result = await pauseAgentCollaborationTask(id)
    if (result.status === 200) {
      ElMessage.success('任务已暂停')
      pageRef.value?.getPageData()
    } else {
      ElMessage.error(result.message || '暂停任务失败')
    }
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

const handleDelete = async (row: AgentCollaborationTask) => {
  try {
    await ElMessageBox.confirm(
      `确认删除任务"${row.name}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消'
      }
    )

    const result = await deleteAgentCollaborationTask(row.id)
    if (result.status === 200) {
      ElMessage.success('删除成功')
      pageRef.value?.getPageData()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleViewMetrics = async (id: string) => {
  try {
    const result = await getAgentCollaborationTaskMetrics(id)
    if (result.status === 200) {
      taskMetrics.value = result.data
      metricsDialogVisible.value = true
    } else {
      ElMessage.error(result.message || '获取性能指标失败')
    }
  } catch (error) {
    ElMessage.error('获取性能指标失败')
  }
}

const handleSelectionChange = (selection: AgentCollaborationTask[]) => {
  console.log('Selected items:', selection)
}

// 辅助方法
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

const getTaskTypeName = (type: string) => {
  const typeMap = {
    'sequential': '顺序执行',
    'parallel': '并行执行',
    'conditional': '条件执行',
    'loop': '循环执行'
  }
  return typeMap[type] || type
}

const getTaskTypeTag = (type: string) => {
  const tagMap = {
    'sequential': '',
    'parallel': 'success',
    'conditional': 'warning',
    'loop': 'info'
  }
  return tagMap[type] || ''
}

const getTaskStatusName = (status: string) => {
  const statusMap = {
    'draft': '草稿',
    'active': '执行中',
    'paused': '暂停',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getTaskStatusTag = (status: string) => {
  const tagMap = {
    'draft': 'info',
    'active': 'success',
    'paused': 'warning',
    'completed': '',
    'failed': 'danger'
  }
  return tagMap[status] || ''
}

const getPriorityName = (priority: string) => {
  const priorityMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return priorityMap[priority] || priority
}

const getPriorityTag = (priority: string) => {
  const tagMap = {
    'low': 'info',
    'medium': '',
    'high': 'warning',
    'urgent': 'danger'
  }
  return tagMap[priority] || ''
}

const getRoleName = (role: string) => {
  const roleMap = {
    'coordinator': '协调者',
    'executor': '执行者',
    'monitor': '监控者',
    'reviewer': '审核者'
  }
  return roleMap[role] || role
}

const getRoleTag = (role: string) => {
  const tagMap = {
    'coordinator': 'danger',
    'executor': 'success',
    'monitor': 'warning',
    'reviewer': 'info'
  }
  return tagMap[role] || ''
}

const getAgentStatusName = (status: string) => {
  const statusMap = {
    'available': '可用',
    'busy': '忙碌',
    'offline': '离线',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getAgentStatusTag = (status: string) => {
  const tagMap = {
    'available': 'success',
    'busy': 'warning',
    'offline': 'info',
    'error': 'danger'
  }
  return tagMap[status] || ''
}

const getStepStatusName = (status: string) => {
  const statusMap = {
    'pending': '等待中',
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败',
    'skipped': '已跳过'
  }
  return statusMap[status] || status
}

const getStepStatusTag = (status: string) => {
  const tagMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'skipped': ''
  }
  return tagMap[status] || ''
}

const getStepTimelineType = (status: string) => {
  const typeMap = {
    'pending': 'info',
    'running': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'skipped': 'info'
  }
  return typeMap[status] || 'info'
}

const getStepIcon = (status: string) => {
  const iconMap = {
    'pending': Loading,
    'running': Loading,
    'completed': SuccessFilled,
    'failed': CircleCloseFilled,
    'skipped': Warning
  }
  return iconMap[status] || Loading
}

const getStepTypeName = (type: string) => {
  const typeMap = {
    'action': '执行动作',
    'decision': '决策判断',
    'parallel': '并行处理',
    'merge': '结果合并'
  }
  return typeMap[type] || type
}

const getAgentName = (agentId: string) => {
  if (!currentTask.value) return agentId
  const agent = currentTask.value.participants.find(p => p.agentId === agentId)
  return agent ? agent.agentName : agentId
}

const getSuccessRateColor = (rate: number) => {
  if (rate >= 0.9) return '#67C23A'
  if (rate >= 0.7) return '#E6A23C'
  return '#F56C6C'
}

const getAvailabilityColor = (availability: number) => {
  if (availability >= 0.95) return '#67C23A'
  if (availability >= 0.8) return '#E6A23C'
  return '#F56C6C'
}

// 生命周期
onMounted(() => {
  // 页面初始化
})
</script>

<style lang="less" scoped>
.task-manage-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.task-detail {
  .detail-card {
    margin-bottom: 20px;

    .description {
      margin-top: 16px;
      
      p {
        margin: 8px 0;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}

.step-content {
  .step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 16px;
    }
  }

  .step-info {
    p {
      margin: 6px 0;
      color: #606266;
      font-size: 14px;
    }

    .step-result {
      margin-top: 12px;

      pre {
        background: #f5f7fa;
        padding: 12px;
        border-radius: 4px;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
      }
    }

    .step-error {
      margin-top: 12px;
    }
  }
}

.metrics-content {
  .metrics-card {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
