<template>
  <div class="agent-status-page">
    <!-- 工具栏 -->
    <TableToolTemp
      tool-title="智能体状态管理"
      :tool-list="toolList"
    />

    <!-- 统计卡片 -->
    <div class="status-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="status-card available">
            <el-statistic title="可用智能体" :value="statusStats.available">
              <template #suffix>
                <el-icon><SuccessFilled /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card busy">
            <el-statistic title="忙碌智能体" :value="statusStats.busy">
              <template #suffix>
                <el-icon><Loading /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card offline">
            <el-statistic title="离线智能体" :value="statusStats.offline">
              <template #suffix>
                <el-icon><CircleCloseFilled /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card error">
            <el-statistic title="错误智能体" :value="statusStats.error">
              <template #suffix>
                <el-icon><Warning /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <PageTable
      ref="pageRef"
      :search-form-temp="searchFormTemp"
      :api-function="getAvailableAgentList"
      :table-columns="tableColumns"
      :table-data-handle="tableDataHandle"
      @selection-change="handleSelectionChange"
    >
      <template #default="{ tableData }">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="id" 
            label="智能体ID"
            width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="name" 
            label="智能体名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="type" 
            label="类型"
            width="120"
          >
            <template #default="{ row }">
              <el-tag :type="getAgentTypeTag(row.type)">
                {{ getAgentTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="status" 
            label="状态"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="version" 
            label="版本"
            width="100"
          />
          <el-table-column 
            prop="capabilities" 
            label="能力数量"
            width="100"
          >
            <template #default="{ row }">
              <el-tag type="info">{{ row.capabilities?.length || 0 }}个</el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="endpoint" 
            label="服务端点"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="lastHeartbeat" 
            label="最后心跳"
            width="180"
          >
            <template #default="{ row }">
              {{ formatTime(row.lastHeartbeat) }}
            </template>
          </el-table-column>
          <el-table-column 
            label="操作"
            width="200"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="handleViewDetail(row)"
              >
                查看详情
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'available' || row.status === 'busy' ? 'warning' : 'success'"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 'available' || row.status === 'busy' ? '停止' : '启动' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 智能体详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="智能体详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentAgent" class="agent-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" header="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="智能体ID">{{ currentAgent.id }}</el-descriptions-item>
            <el-descriptions-item label="智能体名称">{{ currentAgent.name }}</el-descriptions-item>
            <el-descriptions-item label="类型">
              <el-tag :type="getAgentTypeTag(currentAgent.type)">
                {{ getAgentTypeName(currentAgent.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTag(currentAgent.status)">
                {{ getStatusName(currentAgent.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="版本">{{ currentAgent.version }}</el-descriptions-item>
            <el-descriptions-item label="服务端点">{{ currentAgent.endpoint }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 能力列表 -->
        <el-card class="detail-card" header="智能体能力">
          <div class="capabilities">
            <el-tag
              v-for="capability in currentAgent.capabilities"
              :key="capability"
              size="large"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ capability }}
            </el-tag>
          </div>
        </el-card>

        <!-- 实时状态 -->
        <el-card v-if="agentMetrics" class="detail-card" header="实时状态">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="CPU使用率">
                  <el-progress :percentage="agentMetrics.metrics.cpuUsage" />
                </el-descriptions-item>
                <el-descriptions-item label="内存使用率">
                  <el-progress :percentage="agentMetrics.metrics.memoryUsage" />
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="活跃连接数">
                  {{ agentMetrics.metrics.activeConnections }}
                </el-descriptions-item>
                <el-descriptions-item label="每分钟请求数">
                  {{ agentMetrics.metrics.requestsPerMinute }}
                </el-descriptions-item>
                <el-descriptions-item label="最后心跳时间">
                  {{ formatTime(agentMetrics.lastHeartbeat) }}
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            :loading="refreshingAgent"
            @click="refreshAgentStatus"
          >
            刷新状态
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量操作"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-content">
        <p>已选择 <strong>{{ selectedAgents.length }}</strong> 个智能体</p>
        <div class="batch-actions">
          <el-button
            type="success"
            :loading="batchLoading"
            @click="handleBatchStart"
            :disabled="!selectedAgents.length"
          >
            批量启动
          </el-button>
          <el-button
            type="warning"
            :loading="batchLoading"
            @click="handleBatchStop"
            :disabled="!selectedAgents.length"
          >
            批量停止
          </el-button>
          <el-button
            type="info"
            :loading="batchLoading"
            @click="handleBatchRestart"
            :disabled="!selectedAgents.length"
          >
            批量重启
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  View, 
  VideoPlay, 
  VideoPause, 
  RefreshRight,
  Operation,
  SuccessFilled,
  Loading,
  CircleCloseFilled,
  Warning
} from '@element-plus/icons-vue'
import TableToolTemp from '@/components/TableToolTemp.vue'
import PageTable from '@/components/PageTable.vue'
import { 
  getAvailableAgentList,
  getAgentStatus
} from '@/api/a2a'

// 页面引用
const pageRef = ref()

// 对话框状态
const detailDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const currentAgent = ref(null)
const agentMetrics = ref(null)
const refreshingAgent = ref(false)
const batchLoading = ref(false)

// 选中的智能体
const selectedAgents = ref([])

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null
const autoRefreshInterval = 30000 // 30秒

// 状态统计
const statusStats = reactive({
  available: 0,
  busy: 0,
  offline: 0,
  error: 0
})

// 工具栏配置
const toolList = [
  {
    name: '刷新状态',
    icon: 'Refresh',
    type: 'primary',
    action: () => handleRefreshAll()
  },
  {
    name: '批量操作',
    icon: 'Operation',
    type: 'success',
    action: () => handleBatchOperation()
  }
]

// 搜索表单配置
const searchFormTemp = [
  {
    label: '智能体名称',
    name: 'name',
    type: 'input',
    placeholder: '请输入智能体名称'
  },
  {
    label: '智能体类型',
    name: 'type',
    type: 'select',
    placeholder: '请选择类型',
    options: [
      { label: 'NLP处理', value: 'nlp' },
      { label: '知识库', value: 'knowledge' },
      { label: '文本生成', value: 'generation' },
      { label: '内容审核', value: 'moderation' },
      { label: '其他', value: 'other' }
    ]
  },
  {
    label: '状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '可用', value: 'available' },
      { label: '调试', value: 'debug' },
      { label: '离线', value: 'offline' },
      { label: '错误', value: 'error' }
    ]
  }
]

// 表格列配置
const tableColumns = [
  { type: 'selection', width: 55 },
  { 
    prop: 'id', 
    label: '智能体ID',
    width: 150,
    showOverflowTooltip: true
  },
  { 
    prop: 'name', 
    label: '智能体名称',
    minWidth: 200,
    showOverflowTooltip: true
  },
  { 
    prop: 'type', 
    label: '类型',
    width: 120,
    formatter: (row: any) => getAgentTypeName(row.type)
  },
  { 
    prop: 'status', 
    label: '状态',
    width: 100,
    formatter: (row: any) => getStatusName(row.status)
  },
  { 
    prop: 'version', 
    label: '版本',
    width: 100
  },
  { 
    prop: 'capabilities', 
    label: '能力数量',
    width: 100,
    formatter: (row: any) => `${row.capabilities?.length || 0}个`
  },
  { 
    prop: 'endpoint', 
    label: '服务端点',
    minWidth: 200,
    showOverflowTooltip: true
  }
]

// 表格数据处理
const tableDataHandle = (data: any[]) => {
  // 更新状态统计
  updateStatusStats(data)

  return data.map(item => ({
    ...item,
    actions: [
      {
        name: '查看详情',
        icon: 'View',
        type: 'primary',
        action: () => handleViewDetail(item)
      },
      {
        name: item.status === 'available' || item.status === 'busy' ? '停止' : '启动',
        icon: item.status === 'available' || item.status === 'busy' ? 'VideoPause' : 'VideoPlay',
        type: item.status === 'available' || item.status === 'busy' ? 'warning' : 'success',
        action: () => handleToggleAgent(item)
      },
      {
        name: '重启',
        icon: 'RefreshRight',
        type: 'info',
        action: () => handleRestartAgent(item)
      }
    ]
  }))
}

// 方法
const handleViewDetail = async (agent: any) => {
  currentAgent.value = agent
  detailDialogVisible.value = true
  
  // 获取智能体实时状态
  await loadAgentMetrics(agent.id)
}

const loadAgentMetrics = async (agentId: string) => {
  try {
    const result = await getAgentStatus(agentId)
    if (result.status === 200) {
      agentMetrics.value = result.data
    }
  } catch (error) {
    console.error('获取智能体状态失败:', error)
  }
}

const refreshAgentStatus = async () => {
  if (!currentAgent.value) return
  
  refreshingAgent.value = true
  await loadAgentMetrics(currentAgent.value.id)
  refreshingAgent.value = false
  ElMessage.success('状态已刷新')
}

const handleToggleAgent = async (agent: any) => {
  const action = agent.status === 'available' || agent.status === 'busy' ? '停止' : '启动'
  ElMessage.info(`${action}智能体功能开发中...`)
}

const handleRestartAgent = async (agent: any) => {
  ElMessage.info('重启智能体功能开发中...')
}

const handleRefreshAll = () => {
  pageRef.value?.getPageData()
  ElMessage.success('状态已刷新')
}

const handleBatchOperation = () => {
  if (!selectedAgents.value.length) {
    ElMessage.warning('请先选择要操作的智能体')
    return
  }
  batchDialogVisible.value = true
}

const handleBatchStart = async () => {
  batchLoading.value = true
  // 模拟批量启动
  setTimeout(() => {
    batchLoading.value = false
    batchDialogVisible.value = false
    ElMessage.success(`已启动 ${selectedAgents.value.length} 个智能体`)
  }, 2000)
}

const handleBatchStop = async () => {
  batchLoading.value = true
  // 模拟批量停止
  setTimeout(() => {
    batchLoading.value = false
    batchDialogVisible.value = false
    ElMessage.success(`已停止 ${selectedAgents.value.length} 个智能体`)
  }, 2000)
}

const handleBatchRestart = async () => {
  batchLoading.value = true
  // 模拟批量重启
  setTimeout(() => {
    batchLoading.value = false
    batchDialogVisible.value = false
    ElMessage.success(`已重启 ${selectedAgents.value.length} 个智能体`)
  }, 2000)
}

const handleSelectionChange = (selection: any[]) => {
  selectedAgents.value = selection
}

const updateStatusStats = (data: any[]) => {
  statusStats.available = data.filter(item => item.status === 'available').length
  statusStats.busy = data.filter(item => item.status === 'busy').length
  statusStats.offline = data.filter(item => item.status === 'offline').length
  statusStats.error = data.filter(item => item.status === 'error').length
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = setInterval(() => {
    pageRef.value?.getPageData()
  }, autoRefreshInterval)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 辅助方法
const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

const getAgentTypeName = (type: string) => {
  const typeMap = {
    'nlp': 'NLP处理',
    'knowledge': '知识库',
    'generation': '文本生成',
    'moderation': '内容审核',
    'other': '其他'
  }
  return typeMap[type] || type
}

const getAgentTypeTag = (type: string) => {
  const tagMap = {
    'nlp': 'primary',
    'knowledge': 'success',
    'generation': 'warning',
    'moderation': 'danger',
    'other': 'info'
  }
  return tagMap[type] || ''
}

const getStatusName = (status: string) => {
  const statusMap = {
    'available': '可用',
    'busy': '忙碌',
    'offline': '离线',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap = {
    'available': 'success',
    'busy': 'warning',
    'offline': 'info',
    'error': 'danger'
  }
  return tagMap[status] || ''
}

// 生命周期
onMounted(() => {
  // 启动自动刷新
  startAutoRefresh()
})

onUnmounted(() => {
  // 停止自动刷新
  stopAutoRefresh()
})
</script>

<style lang="less" scoped>
.agent-status-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.status-cards {
  margin-bottom: 20px;

  .status-card {
    text-align: center;
    
    :deep(.el-card__body) {
      padding: 20px;
    }

    &.available {
      :deep(.el-statistic__content) {
        color: #67C23A;
      }
    }

    &.busy {
      :deep(.el-statistic__content) {
        color: #E6A23C;
      }
    }

    &.offline {
      :deep(.el-statistic__content) {
        color: #909399;
      }
    }

    &.error {
      :deep(.el-statistic__content) {
        color: #F56C6C;
      }
    }
  }
}

.agent-detail {
  .detail-card {
    margin-bottom: 20px;
  }

  .capabilities {
    min-height: 60px;
  }
}

.batch-content {
  text-align: center;

  p {
    margin-bottom: 20px;
    font-size: 16px;
  }

  .batch-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
