<template>
  <div class="protocol-config-page">
    <!-- 工具栏 -->
    <TableToolTemp
      tool-title="A2A协议配置管理"
      :tool-list="toolList"
    />

    <!-- 数据表格 -->
    <PageTable
      ref="pageRef"
      :search-form-temp="searchFormTemp"
      :api-function="getA2AProtocolList"
      :table-columns="tableColumns"
      :table-data-handle="tableDataHandle"
      @selection-change="handleSelectionChange"
    >
      <template #default="{ tableData }">
        <el-table
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column 
            prop="name" 
            label="协议名称"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="version" 
            label="版本"
            width="100"
          />
          <el-table-column 
            prop="endpoint" 
            label="服务端点"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column 
            prop="authType" 
            label="认证类型"
            width="120"
          >
            <template #default="{ row }">
              <el-tag :type="getAuthTypeTagType(row.authType)">
                {{ getAuthTypeLabel(row.authType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="status" 
            label="状态"
            width="100"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            prop="lastUsed" 
            label="最后使用时间"
            width="180"
          >
            <template #default="{ row }">
              {{ formatTime(row.lastUsed) }}
            </template>
          </el-table-column>
          <el-table-column 
            prop="updatedAt" 
            label="更新时间"
            width="180"
          >
            <template #default="{ row }">
              {{ formatTime(row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column 
            label="操作"
            width="200"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="warning"
                @click="handleTestConnection(row.id)"
              >
                测试连接
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </PageTable>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="协议名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入协议名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议版本" prop="version">
              <el-input
                v-model="formData.version"
                placeholder="请输入协议版本，如：1.0.0"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="协议描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入协议描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务端点" prop="endpoint">
              <el-input
                v-model="formData.endpoint"
                placeholder="请输入服务端点URL"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="认证类型" prop="authType">
              <el-select
                v-model="formData.authType"
                placeholder="请选择认证类型"
                style="width: 100%"
                @change="handleAuthTypeChange"
              >
                <el-option label="无认证" value="none" />
                <el-option label="Token认证" value="token" />
                <el-option label="OAuth认证" value="oauth" />
                <el-option label="基础认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 认证配置 -->
        <el-form-item v-if="formData.authType !== 'none'" label="认证配置">
          <div class="auth-config">
            <!-- Token认证 -->
            <template v-if="formData.authType === 'token'">
              <el-input
                v-model="formData.authConfig.token"
                placeholder="请输入访问Token"
                style="margin-bottom: 10px"
              />
              <el-input
                v-model="formData.authConfig.refreshToken"
                placeholder="请输入刷新Token（可选）"
              />
            </template>

            <!-- OAuth认证 -->
            <template v-if="formData.authType === 'oauth'">
              <el-input
                v-model="formData.authConfig.clientId"
                placeholder="请输入Client ID"
                style="margin-bottom: 10px"
              />
              <el-input
                v-model="formData.authConfig.clientSecret"
                placeholder="请输入Client Secret"
                type="password"
                style="margin-bottom: 10px"
              />
              <el-input
                v-model="formData.authConfig.scope"
                placeholder="请输入授权范围（可选）"
              />
            </template>

            <!-- 基础认证 -->
            <template v-if="formData.authType === 'basic'">
              <el-input
                v-model="formData.authConfig.username"
                placeholder="请输入用户名"
                style="margin-bottom: 10px"
              />
              <el-input
                v-model="formData.authConfig.password"
                placeholder="请输入密码"
                type="password"
              />
            </template>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="超时时间" prop="timeout">
              <el-input-number
                v-model="formData.timeout"
                :min="1000"
                :max="300000"
                :step="1000"
                controls-position="right"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #909399; font-size: 12px">毫秒</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重试次数" prop="retryCount">
              <el-input-number
                v-model="formData.retryCount"
                :min="0"
                :max="10"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 连接测试对话框 -->
    <el-dialog
      v-model="testDialogVisible"
      title="连接测试"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="test-content">
        <div v-if="testLoading" class="test-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在测试连接...</span>
        </div>
        <div v-else-if="testResult" class="test-result">
          <div v-if="testResult.data.connected" class="success-result">
            <el-icon color="#67C23A"><SuccessFilled /></el-icon>
            <div class="result-info">
              <h4>连接成功</h4>
              <p>响应时间: {{ testResult.data.responseTime }}ms</p>
              <p>协议版本: {{ testResult.data.version }}</p>
              <p>支持能力: {{ testResult.data.capabilities?.join(', ') }}</p>
            </div>
          </div>
          <div v-else class="error-result">
            <el-icon color="#F56C6C"><CircleCloseFilled /></el-icon>
            <div class="result-info">
              <h4>连接失败</h4>
              <p>错误信息: {{ testResult.data.error }}</p>
              <p>失败时间: {{ formatTime(testResult.data.lastError) }}</p>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="testDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            :loading="testLoading"
            @click="handleTestConnection(currentTestId)"
          >
            重新测试
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Edit, 
  Delete, 
  Connection,
  Loading,
  SuccessFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'
import TableToolTemp from '@/components/TableToolTemp.vue'
import PageTable from '@/components/PageTable.vue'
import { 
  getA2AProtocolList,
  createA2AProtocol,
  updateA2AProtocol,
  deleteA2AProtocol,
  testA2AProtocolConnection
} from '@/api/a2a'
import type { A2AProtocolConfig } from '@/mock/a2a'

// 页面引用
const pageRef = ref()
const formRef = ref()

// 对话框状态
const dialogVisible = ref(false)
const testDialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const testLoading = ref(false)
const currentTestId = ref('')
const testResult = ref(null)

// 表单数据
const formData = reactive({
  name: '',
  version: '1.0.0',
  description: '',
  endpoint: '',
  authType: 'none',
  authConfig: {},
  timeout: 30000,
  retryCount: 3
})

// 重置表单数据
const resetFormData = () => {
  formData.name = ''
  formData.version = '1.0.0'
  formData.description = ''
  formData.endpoint = ''
  formData.authType = 'none'
  formData.authConfig = {}
  formData.timeout = 30000
  formData.retryCount = 3
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入协议名称', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入协议版本', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本格式不正确，请使用x.y.z格式', trigger: 'blur' }
  ],
  endpoint: [
    { required: true, message: '请输入服务端点', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  authType: [
    { required: true, message: '请选择认证类型', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑A2A协议配置' : '新增A2A协议配置')

// 工具栏配置
const toolList = [
  {
    name: '新增',
    icon: 'Plus',
    type: 'primary',
    action: () => handleAdd()
  }
]

// 搜索表单配置
const searchFormTemp = [
  {
    label: '协议名称',
    name: 'name',
    type: 'input',
    placeholder: '请输入协议名称'
  },
  {
    label: '状态',
    name: 'status',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '激活', value: 'active' },
      { label: '未激活', value: 'inactive' },
      { label: '错误', value: 'error' }
    ]
  }
]

// 表格列配置
const tableColumns = [
  { type: 'selection', width: 55 },
  { 
    prop: 'name', 
    label: '协议名称',
    minWidth: 150,
    showOverflowTooltip: true
  },
  { 
    prop: 'version', 
    label: '版本',
    width: 100
  },
  { 
    prop: 'endpoint', 
    label: '服务端点',
    minWidth: 200,
    showOverflowTooltip: true
  },
  { 
    prop: 'authType', 
    label: '认证类型',
    width: 120,
    formatter: (row: any) => {
      const authTypeMap = {
        'none': '无认证',
        'token': 'Token认证',
        'oauth': 'OAuth认证',
        'basic': '基础认证'
      }
      return authTypeMap[row.authType] || row.authType
    }
  },
  { 
    prop: 'status', 
    label: '状态',
    width: 100,
    formatter: (row: any) => {
      const statusMap = {
        'active': '激活',
        'inactive': '未激活',
        'error': '错误'
      }
      return statusMap[row.status] || row.status
    }
  },
  { 
    prop: 'lastUsed', 
    label: '最后使用时间',
    width: 180,
    formatter: (row: any) => formatTime(row.lastUsed)
  },
  { 
    prop: 'updatedAt', 
    label: '更新时间',
    width: 180,
    formatter: (row: any) => formatTime(row.updatedAt)
  }
]

// 表格数据处理
const tableDataHandle = (data: A2AProtocolConfig[]) => {
  return data.map(item => ({
    ...item,
    actions: [
      {
        name: '编辑',
        icon: 'Edit',
        type: 'primary',
        action: () => handleEdit(item)
      },
      {
        name: '测试连接',
        icon: 'Connection',
        type: 'warning',
        action: () => handleTestConnection(item.id)
      },
      {
        name: '删除',
        icon: 'Delete',
        type: 'danger',
        action: () => handleDelete(item)
      }
    ]
  }))
}

// 方法
const handleAdd = () => {
  resetFormData()
  isEdit.value = false
  dialogVisible.value = true
}

const handleEdit = (row: A2AProtocolConfig) => {
  Object.assign(formData, row)
  isEdit.value = true
  dialogVisible.value = true
}

const handleDelete = async (row: A2AProtocolConfig) => {
  try {
    await ElMessageBox.confirm(
      `确认删除协议配置"${row.name}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消'
      }
    )

    const result = await deleteA2AProtocol(row.id)
    if (result.status === 200) {
      ElMessage.success('删除成功')
      pageRef.value?.getPageData()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleTestConnection = async (id: string) => {
  currentTestId.value = id
  testDialogVisible.value = true
  testLoading.value = true
  testResult.value = null

  try {
    const result = await testA2AProtocolConnection(id)
    testResult.value = result
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    testLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    let result
    if (isEdit.value) {
      result = await updateA2AProtocol(formData.id, formData)
    } else {
      result = await createA2AProtocol(formData)
    }

    if (result.status === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      pageRef.value?.getPageData()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

const handleAuthTypeChange = (value: string) => {
  // 清空认证配置
  formData.authConfig = {}
}

const handleSelectionChange = (selection: A2AProtocolConfig[]) => {
  // 处理批量选择
  console.log('Selected items:', selection)
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 认证类型标签样式和标签
const getAuthTypeTagType = (authType: string): 'info' | 'success' | 'warning' | 'danger' => {
  const typeMap: Record<string, 'info' | 'success' | 'warning' | 'danger'> = {
    'none': 'info',
    'token': 'success',
    'oauth': 'warning',
    'basic': 'danger'
  }
  return typeMap[authType] || 'info'
}

const getAuthTypeLabel = (authType: string) => {
  const labelMap: Record<string, string> = {
    'none': '无认证',
    'token': 'Token认证',
    'oauth': 'OAuth认证',
    'basic': '基础认证'
  }
  return labelMap[authType] || authType
}

// 状态标签样式和标签
const getStatusTagType = (status: string): 'info' | 'success' | 'danger' => {
  const typeMap: Record<string, 'info' | 'success' | 'danger'> = {
    'active': 'success',
    'inactive': 'info',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'active': '激活',
    'inactive': '未激活',
    'error': '错误'
  }
  return labelMap[status] || status
}

// 生命周期
onMounted(() => {
  // 页面初始化
})
</script>

<style lang="less" scoped>
.protocol-config-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.auth-config {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.test-content {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  .test-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: #909399;

    .el-icon {
      font-size: 32px;
    }
  }

  .test-result {
    width: 100%;

    .success-result,
    .error-result {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .el-icon {
        font-size: 24px;
        margin-top: 4px;
      }

      .result-info {
        flex: 1;

        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
        }

        p {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
