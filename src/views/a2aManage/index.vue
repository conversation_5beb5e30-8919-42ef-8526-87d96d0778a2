<template>
  <div class="a2a-management-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>A2A多智能体协同管理</h1>
      <p>Agent-to-Agent 协议配置与智能体协同任务管理平台</p>
    </div>

    <div class="a2a-container">
      <!-- 左侧导航菜单 -->
      <div class="a2a-sidebar">
        <div class="nav-section">
          <h3>协议配置</h3>
          <ul class="nav-list">
            <li 
              v-for="item in protocolItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>任务管理</h3>
          <ul class="nav-list">
            <li 
              v-for="item in taskItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>监控分析</h3>
          <ul class="nav-list">
            <li 
              v-for="item in monitorItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3>智能体状态</h3>
          <ul class="nav-list">
            <li 
              v-for="item in agentItems" 
              :key="item.key"
              :class="{ active: activeTab === item.key }"
              @click="selectTab(item.key)"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.label }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="a2a-content">
        <div class="content-header">
          <div class="breadcrumb">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>A2A管理</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentTabLabel }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="content-actions">
            <el-button @click="refreshContent" :loading="refreshing">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button @click="openInNewTab">
              <el-icon><Link /></el-icon>
              新窗口打开
            </el-button>
          </div>
        </div>

        <div class="content-body">
          <!-- A2A协议配置 -->
          <div v-if="activeTab === 'protocol-config'" class="tab-content">
            <protocolConfig />
          </div>

          <!-- 协议测试 -->
          <div v-else-if="activeTab === 'protocol-test'" class="tab-content">
            <div class="coming-soon">
              <el-empty description="协议测试功能开发中...">
                <el-button type="primary" @click="selectTab('protocol-config')">
                  查看协议配置
                </el-button>
              </el-empty>
            </div>
          </div>

          <!-- 任务管理 -->
          <div v-else-if="activeTab === 'task-manage'" class="tab-content">
            <taskManage />
          </div>

          <!-- 任务编排 -->
          <div v-else-if="activeTab === 'task-orchestration'" class="tab-content">
            <div class="coming-soon">
              <el-empty description="任务编排功能开发中...">
                <el-button type="primary" @click="selectTab('task-manage')">
                  查看任务管理
                </el-button>
              </el-empty>
            </div>
          </div>

          <!-- 通信记录 -->
          <div v-else-if="activeTab === 'communication-log'" class="tab-content">
            <communicationLog />
          </div>

          <!-- 性能监控 -->
          <div v-else-if="activeTab === 'performance-monitor'" class="tab-content">
            <div class="coming-soon">
              <el-empty description="性能监控功能开发中...">
                <el-button type="primary" @click="selectTab('communication-log')">
                  查看通信记录
                </el-button>
              </el-empty>
            </div>
          </div>

          <!-- 智能体状态 -->
          <div v-else-if="activeTab === 'agent-status'" class="tab-content">
            <agentStatus />
          </div>

          <!-- 智能体注册 -->
          <div v-else-if="activeTab === 'agent-registry'" class="tab-content">
            <div class="coming-soon">
              <el-empty description="智能体注册功能开发中...">
                <el-button type="primary" @click="selectTab('agent-status')">
                  查看智能体状态
                </el-button>
              </el-empty>
            </div>
          </div>

          <!-- 默认欢迎页面 -->
          <div v-else class="welcome-content">
            <div class="welcome-card">
              <h2>欢迎使用 A2A 多智能体协同管理平台</h2>
              <p>这里是基于 Agent-to-Agent 协议的智能体协同管理系统，提供以下功能：</p>
              
              <div class="feature-grid">
                <div class="feature-item">
                  <el-icon class="feature-icon" color="#409EFF"><Setting /></el-icon>
                  <h3>协议配置</h3>
                  <p>管理 A2A 协议配置，支持多种认证方式和连接测试</p>
                </div>
                <div class="feature-item">
                  <el-icon class="feature-icon" color="#67C23A"><Operation /></el-icon>
                  <h3>任务管理</h3>
                  <p>创建和管理智能体协同任务，支持多种执行模式</p>
                </div>
                <div class="feature-item">
                  <el-icon class="feature-icon" color="#E6A23C"><DataAnalysis /></el-icon>
                  <h3>监控分析</h3>
                  <p>实时监控智能体通信状态和性能指标</p>
                </div>
                <div class="feature-item">
                  <el-icon class="feature-icon" color="#F56C6C"><Monitor /></el-icon>
                  <h3>智能体状态</h3>
                  <p>查看和管理所有注册智能体的运行状态</p>
                </div>
              </div>

              <div class="quick-actions">
                <el-button type="primary" @click="selectTab('protocol-config')">
                  开始配置协议
                </el-button>
                <el-button type="success" @click="selectTab('task-manage')">
                  查看协同任务
                </el-button>
                <el-button type="info" @click="selectTab('communication-log')">
                  查看通信记录
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { 
  Setting, 
  Connection, 
  Operation, 
  Share, 
  ChatLineRound, 
  DataAnalysis, 
  Monitor, 
  User,
  Refresh, 
  Link
} from '@element-plus/icons-vue'

// 导入A2A相关组件
import protocolConfig from './protocolConfig.vue'
import taskManage from './taskManage.vue'
import communicationLog from './communicationLog.vue'
import agentStatus from './agentStatus.vue'

// 导航菜单配置
const protocolItems = [
  { key: 'protocol-config', label: 'A2A协议配置', icon: 'Setting' },
  { key: 'protocol-test', label: '协议连接测试', icon: 'Connection' }
]

const taskItems = [
  { key: 'task-manage', label: '协同任务管理', icon: 'Operation' },
  { key: 'task-orchestration', label: '任务编排', icon: 'Share' }
]

const monitorItems = [
  { key: 'communication-log', label: '通信记录', icon: 'ChatLineRound' },
  { key: 'performance-monitor', label: '性能监控', icon: 'DataAnalysis' }
]

const agentItems = [
  { key: 'agent-status', label: '智能体状态', icon: 'Monitor' },
  { key: 'agent-registry', label: '智能体注册', icon: 'User' }
]

// 响应式数据
const activeTab = ref('')
const refreshing = ref(false)

// 计算属性
const currentTabLabel = computed(() => {
  const allItems = [...protocolItems, ...taskItems, ...monitorItems, ...agentItems]
  const currentItem = allItems.find(item => item.key === activeTab.value)
  return currentItem ? currentItem.label : '欢迎'
})

// 方法
const selectTab = (key: string) => {
  activeTab.value = key
}

const selectDefaultTab = () => {
  selectTab('protocol-config')
}

const refreshContent = () => {
  refreshing.value = true
  // 触发子组件刷新
  setTimeout(() => {
    refreshing.value = false
  }, 1000)
}

const openInNewTab = () => {
  // 根据当前标签页打开对应的独立页面
  const tabRoutes = {
    'protocol-config': '/chatbot/a2aProtocolConfig',
    'protocol-test': '/chatbot/a2aProtocolConfig?tab=test',
    'task-manage': '/chatbot/a2aTaskManage',
    'task-orchestration': '/chatbot/a2aTaskManage?tab=orchestration',
    'communication-log': '/chatbot/a2aCommunicationLog',
    'performance-monitor': '/chatbot/a2aCommunicationLog?tab=monitor',
    'agent-status': '/chatbot/a2aAgentStatus',
    'agent-registry': '/chatbot/a2aAgentStatus?tab=registry'
  }
  
  const route = tabRoutes[activeTab.value]
  if (route) {
    window.open(route, '_blank')
  }
}

// 生命周期
onMounted(() => {
  // 检查URL参数，如果有embed参数则隐藏侧边栏
  const urlParams = new URLSearchParams(window.location.search)
  const isEmbedded = urlParams.get('embed') === 'true'
  
  if (isEmbedded) {
    // 嵌入模式下隐藏侧边栏，直接显示默认内容
    document.querySelector('.a2a-sidebar')?.setAttribute('style', 'display: none;')
  }
  
  // 默认显示欢迎页面
  activeTab.value = ''
})
</script>

<style lang="less" scoped>
.a2a-management-page {
  height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    p {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }

  .a2a-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .a2a-sidebar {
      width: 280px;
      background: white;
      border-right: 1px solid #e4e7ed;
      overflow-y: auto;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

      .nav-section {
        padding: 20px 0;

        h3 {
          margin: 0 0 15px 20px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          border-left: 3px solid #409eff;
          padding-left: 15px;
        }

        .nav-list {
          list-style: none;
          margin: 0;
          padding: 0;

          li {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;

            &:hover {
              background: #f5f7fa;
              border-left-color: #409eff;
            }

            &.active {
              background: #ecf5ff;
              border-left-color: #409eff;
              color: #409eff;
            }

            .el-icon {
              margin-right: 12px;
              font-size: 18px;
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }

    .a2a-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;

      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #e4e7ed;
        background: #fafafa;

        .breadcrumb {
          .el-breadcrumb {
            font-size: 14px;
          }
        }

        .content-actions {
          display: flex;
          gap: 10px;

          .el-button {
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }
      }

      .content-body {
        flex: 1;
        overflow: auto;

        .tab-content {
          height: 100%;
        }

        .coming-soon {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #fafafa;

          .el-empty {
            text-align: center;

            .el-button {
              margin-top: 20px;
            }
          }
        }

        .welcome-content {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          background: #fafafa;
          padding: 40px;

          .welcome-card {
            max-width: 800px;
            text-align: center;

            h2 {
              margin: 0 0 16px 0;
              font-size: 24px;
              color: #303133;
            }

            > p {
              margin: 0 0 40px 0;
              font-size: 16px;
              color: #606266;
              line-height: 1.6;
            }

            .feature-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 30px;
              margin-bottom: 40px;

              .feature-item {
                padding: 20px;
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                background: white;
                transition: all 0.3s ease;

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }

                .feature-icon {
                  font-size: 32px;
                  margin-bottom: 12px;
                }

                h3 {
                  margin: 0 0 8px 0;
                  font-size: 18px;
                  color: #303133;
                }

                p {
                  margin: 0;
                  font-size: 14px;
                  color: #606266;
                  line-height: 1.5;
                }
              }
            }

            .quick-actions {
              display: flex;
              justify-content: center;
              gap: 16px;
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .a2a-sidebar {
    width: 240px !important;
  }

  .feature-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .a2a-container {
    flex-direction: column !important;

    .a2a-sidebar {
      width: 100% !important;
      height: auto !important;
      max-height: 200px;
    }
  }

  .quick-actions {
    flex-direction: column !important;
    align-items: center;

    .el-button {
      width: 200px;
    }
  }
}
</style>
