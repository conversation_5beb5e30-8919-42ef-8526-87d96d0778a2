/**
 * MOCK数据统一配置
 * 在这里统一管理所有模块的MOCK开关
 */

// ============= MOCK开关配置 =============

// 用户管理模块
export const USER_MOCK_ENABLED = true

// MCP模块
export const MCP_MOCK_ENABLED = true

// A2A协议模块
export const A2A_MOCK_ENABLED = true

// 其他模块可以在这里添加
// export const ORDER_MOCK_ENABLED = false
// export const PAYMENT_MOCK_ENABLED = false

// ============= 全局MOCK开关 =============

/**
 * 全局MOCK开关
 * 设置为 true 时，所有启用的模块都会使用MOCK数据
 * 设置为 false 时，所有模块都会使用真实API
 */
export const GLOBAL_MOCK_ENABLED = true

// ============= 环境检测 =============

/**
 * 根据环境自动判断是否启用MOCK
 * 开发环境默认启用，生产环境默认禁用
 */
export const isDevelopment = () => {
  return process.env.NODE_ENV === 'development' || 
         process.env.NODE_ENV === 'test' ||
         window.location.hostname === 'localhost' ||
         window.location.hostname === '127.0.0.1'
}

/**
 * 获取最终的MOCK状态
 * @param moduleEnabled 模块特定的MOCK开关
 * @returns 是否应该使用MOCK数据
 */
export const shouldUseMock = (moduleEnabled: boolean = true): boolean => {
  const isDevEnv = isDevelopment()
  const result = GLOBAL_MOCK_ENABLED && moduleEnabled && isDevEnv
  
  return result
}

// ============= 导出配置 =============

export default {
  USER_MOCK_ENABLED,
  MCP_MOCK_ENABLED,
  A2A_MOCK_ENABLED,
  GLOBAL_MOCK_ENABLED,
  isDevelopment,
  shouldUseMock
}
